from utils import utils
import json
import time
from jsonpath import *

# 使用前提
# 1、在新Apollo中创建项目
# 2、在新旧Apollo中添加第三方应用授权，管理员工具 - 开放平台授权管理，创建第三方应用生成token，并将appId授权给token

# 旧Apollo配置信息
fromHost = 'http://**************:8070/'
fromAppid = 'zcp30'
fromToken = '471ca575a3a802db1841a69f0ea434e202a2aecc'
fromEnv = 'DEV'
fromClusters = 'default'

# 新Apollo配置信息
toHost = 'http://**************:8070/'
toAppid = 'zcp110'
toToken = '471ca575a3a802db1841a69f0ea434e202a2aecc'
toEnv = 'DEV'
toClusters = 'default'

logger = utils.log()

# 将namespace和配置项输出到json文件
def exportConfig():
    headers = {'Authorization':fromToken}
    path = fromHost + 'openapi/v1/envs/'+fromEnv+'/apps/'+fromAppid+'/clusters/'+fromClusters+'/namespaces'
    
    # 查询项目下的所有namespace和配置项信息
    result = utils.call(None, path, 'GET', headers=headers)
    result = json.loads(result['body'])
    configDict = []

    # 将所有namespace及其对应的配置项保存到文件
    for i in result:
        str = {}
        items = {}
        str['namespace'] = i['namespaceName']

        for j in i['items']:
            if j['key']:
                items[j['key']] = j['value']
        str['items'] = items
        configDict.append(str)
    with open('src\Apollo\output.json', 'w', encoding='utf-8') as file:
        json.dump(configDict, file, ensure_ascii=False, indent=4)

def importConfig():
    headers = {'Authorization':toToken}

    # 读取上一步保存的json文件
    path_namespace = toHost + 'openapi/v1/apps/'+toAppid+'/appnamespaces'
    with open('src\Apollo\output.json', 'r', encoding='utf-8') as file:
        configDict = json.load(file)
    
    # 循环读取每个namespace
    for i in configDict:
        i = dict(i)
        namespace = i['namespace']
        # 判断namespace是否已存在
        path_exist = toHost + 'openapi/v1/envs/'+toEnv+'/apps/'+toAppid+'/clusters/'+toClusters+'/namespaces/'+namespace
        exist = utils.call(None, path=path_exist, method='GET', headers=headers)
        logger.info('判断namespace是否存在，接口地址：%s，接口返回：%s',path_exist,exist['body'])
        # namespace不存在时，调用创建接口
        if exist['code'] != 200:
            param = {"name":i['namespace'],"appId":toAppid,"format":"properties","isPublic":"false","comment":"","dataChangeCreatedBy":"apollo"}
            result = utils.call(None, path=path_namespace, method='POST', headers=headers, param=param)
            time.sleep(1)
            logger.info('创建namespace，接口地址：%s，传参：%s,接口返回：%s',path_namespace,param,exist['body'])
            if result['code'] != 200:
                logger.error('创建namespace失败，接口地址：%s，传参：%s,接口返回：%s',path_namespace,param,result['body'])
                continue
        path_items = toHost + 'openapi/v1/envs/'+toEnv+'/apps/'+toAppid+'/clusters/'+toClusters+'/namespaces/'+namespace+'/items'
        # 循环读取当前namespace下的所有配置项
        for key,value in i['items'].items():
            param = {"key":key,"value":value,"comment":"","dataChangeCreatedBy":"apollo"}
            # 调用配置项创建接口
            result = utils.call(None, path=path_items, method='POST', headers=headers, param=param)
            time.sleep(0.5)
            logger.info('创建配置项，接口地址：%s，传参：%s,接口返回：%s',path_items,param,result['body'])
            if result['code'] != 200:
                logger.error('创建配置项失败，接口地址：%s，传参：%s,接口返回：%s',path_items,param,result['body'])
        # 发布namespace
        path_release = toHost + 'openapi/v1/envs/'+toEnv+'/apps/'+toAppid+'/clusters/'+toClusters+'/namespaces/'+namespace+'/releases'
        param = {"releaseTitle":"release","releaseComment":"首次发布","releasedBy":"apollo"}
        result = utils.call(None, path=path_release, method='POST', headers=headers, param=param)
        logger.info('发布namespace，接口地址：%s，传参：%s,接口返回：%s',path_release,param,result['body'])
        if result['code'] !=200:
            logger.error('创建namespace失败，接口地址：%s，传参：%s,接口返回：%s',path_release,param,result['body'])
            

    