# -*- coding: utf-8 -*-
import os
from openai import OpenAI

if __name__ == '__main__':

    client = OpenAI(
        api_key = os.environ.get("a86a2946-22c5-4e7a-a08b-8bcaf050fa36"),
        base_url = "https://ark.cn-beijing.volces.com/api/v3",
    )

    # Non-streaming:
    print("----- standard request -----")
    completion = client.chat.completions.create(
        model = "ep-20250211135714-j7sgf",  # your model endpoint ID
        messages = [
            {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
            {"role": "user", "content": "常见的十字花科植物有哪些？"},
        ],
    )
    print(completion.choices[0].message.content)
