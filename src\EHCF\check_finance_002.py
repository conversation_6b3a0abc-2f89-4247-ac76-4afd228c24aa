import datetime
import pymysql
from utils import utils

'''EHCF-金融
每2小时运行一次(和充电桩的错开) 
前一天的开始、截至日期如下，执行周期和之前的一样:
开始日期=当期日期-1 0点0分0秒  截至日期=当期日期-1 12点0分0秒
开始日期=当期日期-1 12点0分0秒  截至日期=当期日期-1 23点59分59秒'''

webhook ='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f9730b84-7a37-400d-a42b-ddcb2a65917a'
username = '13061378580' #重点@某人，为空@全员

def check_EHCF_finance_setup(): 
    Authorization = utils.get_indentity_password_auth('EHCF')
    header={'Ehcforigintag':'1'}

    global headers
    headers={**Authorization,**header} #获取组合后的headers信息

    global today_time1
    today_time=datetime.date.today() #当天
    today_time1 = str(today_time-datetime.timedelta(days=1))#昨天

def check_EHCF_finance_001(): 

    today_time1_1=today_time1+' '+'00:00:00'
    today_time1_2=today_time1+' '+'12:00:00' #开始日期=当期日期-1 0点0分0秒  截至日期=当期日期-1 12点0分0秒
    param = {'syncAll': 'true','serviceProviderCode':'1002','startDate':today_time1_1,'endDate':today_time1_2}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body1
    result_body1=result['body']
    assert "数据" in result_body1,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_finance_002(): 

    today_time1_3=today_time1+' '+'12:00:00'
    today_time1_4=today_time1+' '+'23:59:59' #开始日期=当期日期-1 12点0分0秒  截至日期=当期日期-1 23点59分59秒
    param = {'syncAll': 'true','serviceProviderCode':'1002','startDate':today_time1_3,'endDate':today_time1_4}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body2
    result_body2=result['body']
    assert "数据" in result_body2,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_msg(): #执行失败，发送企业微信消息
    result_body=[]
    result_body.append(result_body1)
    result_body.append(result_body2)

    if result_body:
        # topic=("<font color=blue>EHCF-充电桩-定时任务-当天</font>\n" +
        #        "<font color=red>执行失败</font>\n") #使用markdown设置字体颜色，需修改check_info.json文件
        
        topic=("EHCF-金融-定时任务-昨天-执行失败") 
        content = ''
        arr = []
        for record in result_body:
            if "数据" not in record :
                arr.append(record)
                content = content + '\n' + str(record)
        if arr:
            utils.msgPush(webhook,topic,content,username)    