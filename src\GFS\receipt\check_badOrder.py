# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
import datetime
from decimal import Decimal

from utils import utils
from utils.mysqlOp import MysqlOp
from deepdiff import DeepDiff


def check_badOrderPrice():
    # 实例数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    today = datetime.date.today()
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    yesterday = yesterday.strftime("%Y-%m-%d")
    # 正式环境用
    sql = '''select `Id` , `BadPriceNo` , `OrderBadPrice` from `finance_main`.`main_badpriceinfo`
       where `AuditTime` >='{0}'
       and `AuditTime` <'{1}'
       and `BadPriceStatus` =4
      '''.format(yesterday, today)
    # 测试环境用
    # sql = '''select `Id` , `BadPriceNo` , `OrderBadPrice` from `finance_main`.`main_badpriceinfo`
    #  where `AuditTime` >='2023-10-31'
    #  and `AuditTime` <'2023-11-20'
    #  and `BadPriceStatus` =4
    #  and id ='BP9999999191'
    # '''
    result = utils.select(conn, sql)
    badPrice_lst = []
    temp_dic = {}
    robotId = utils.get_config('gfs_report')['robotId']
    username = '18661922397'
    topic = '昨日新增坏账单异常数据如下，请核实:'
    content = '\n'
    if result:
        for row in result:
            temp_dic['Id'] = row[0]
            temp_dic['BadPriceNo'] = row[1]
            temp_dic['OrderBadPrice'] = row[2]
            badPrice_lst.append(temp_dic)
            temp_dic = {}
    # print(badPrice_lst)
    for i in range(len(badPrice_lst)):
        badOrder_lst = []
        BadPriceId = badPrice_lst[i].get('Id')
        BadPriceNo = badPrice_lst[i].get('BadPriceNo')
        OrderBadPrice = badPrice_lst[i].get('OrderBadPrice')
        sql_badPrice = '''select sum( a.`OrderBadPrice`)  from `finance_main`.`main_badpriceorderinfo` a, `finance_main`.`main_badpriceinfo` b 
        where a.`BadPriceId` =b.`Id` 
        and a.`Deleted` =0
        and b.`Deleted` =0
        and a.`BadPriceId` ='{0}'
        '''.format(BadPriceId)
        result_badPrice = utils.select(conn, sql_badPrice)
        if result_badPrice:
            if result_badPrice[0][0] != OrderBadPrice:
                msg = "坏账单{}坏账总金额与各个订单坏账金额汇总不相等".format(BadPriceNo)
                content = content + msg + '\n'
        sql_badMainPart = '''select sum( `OrderBadPrice`)  from `finance_main`.`main_badpriceordermainpartinfo` where `BadPriceId` ='{0}' and `Deleted` =0
        '''.format(BadPriceId)
        result_badMainPart = utils.select(conn, sql_badMainPart)
        if result_badMainPart:
            if result_badMainPart[0][0] != OrderBadPrice:
                msg = "坏账单{}坏账总金额与各个主体坏账金额汇总不相等".format(BadPriceNo)
                content = content + msg + '\n'
        # 从坏账单包含的订单的维度比对订单三层表中物料的坏账金额和坏账主表Json解析出来的金额是否一致
        # 查询出坏账单包含订单
        sql_badOrder = '''select mb2.OrderId ,bo.OrderNo ,mb2.OrderBadPrice  from finance_main.main_badpriceinfo mb,finance_main.main_badpriceorderinfo mb2 ,
        finance_basic.basic_orderinfo bo
        where mb.Id = mb2.BadPriceId 
        and mb2.OrderId = bo.OrderId 
        and mb.BadPriceStatus =4
        and mb2.Deleted =0
        and mb.Id ='{}'
        '''.format(BadPriceId)
        result_badOrder = utils.select(conn, sql_badOrder)
        if result_badOrder:
            for row in result_badOrder:
                temp_dic["OrderId"] = row[0]
                temp_dic["OrderNo"] = row[1]
                temp_dic["OrderBadPrice"] = row[2]
                badOrder_lst.append(temp_dic)
                temp_dic = {}
        for x in range(len(badOrder_lst)):
            sumMaterialBadPrice = 0
            badRelateMaterial_dic = {}
            orderBadMaterial_dic = {}
            orderId = badOrder_lst[x].get("OrderId")
            orderNo = badOrder_lst[x].get("OrderNo")
            orderBadPrice = badOrder_lst[x].get("OrderBadPrice")
            sql_badRelateMaterial = ''' SELECT B.OrderId,F.MainpartId,F.MainPartName,F.OrderItemDetailId,SUM(F.MaterialBadPrice) AS MaterialBadPrice
            from `finance_main`.`main_badpriceinfo` A
            INNER JOIN `finance_main`.`main_badpriceorderinfo` B 
            on A.`Id` = B.`BadPriceId`
            INNER JOIN JSON_TABLE(B.BadPriceOrderJson,'$[*]' COLUMNS(OrderItemId CHAR(12)  CHARACTER SET utf8 PATH '$.OrderItemId', 
                                                                      NESTED PATH '$.ItemDetailJson[*]' COLUMNS(OrderItemDetailId CHAR(12) CHARACTER SET utf8 PATH '$.OrderItemDetailId',
                                                                                        MainPartId CHAR(12) CHARACTER SET utf8 PATH '$.MainPartId', MainPartName VARCHAR(100) CHARACTER SET utf8 PATH '$.MainPartName',
                                                                                        MaterialBadPrice DECIMAL(18,4) PATH '$.MaterialBadPrice'))) F
            where A.`Deleted` = 0 
            and B.`Deleted` = 0 
            and A.BadPriceStatus = 4
            and B.orderId ='{}'
            GROUP BY F.OrderItemDetailId
            order by F.OrderItemDetailId 
            '''.format(orderId)
            result_badRelateMaterial = utils.select(conn, sql_badRelateMaterial)
            if result_badRelateMaterial:
                for row in result_badRelateMaterial:
                    if row[4] != Decimal('0.0000'):
                        temp_dic["MainpartId"] = row[1]
                        temp_dic["MaterialBadPrice"] = row[4]
                        badRelateMaterial_dic[row[3]] = temp_dic
                        temp_dic = {}
                # print(badRelateMaterial_dic)
            # 订单三层表中物料对应的回款金额
            sql_orderBadMaterial = '''select c.OrderId ,a.MainPartId ,a.MainPartName ,a.OrderItemDetailId ,ifnull(a.MaterialBadPrice,0) as MaterialBadPrice  from `finance_basic`.`basic_orderitemdetail` a
            inner join finance_basic.basic_orderitem b
            on a.OrderItemId =b.OrderItemId 
            inner join finance_basic.basic_orderinfo c
            on b.OrderId =c.OrderId 
            where c.OrderId ='{}'
            '''.format(orderId)
            # print(sql_orderBadMaterial)
            result_orderBadMaterial = utils.select(conn, sql_orderBadMaterial)
            if result_orderBadMaterial:
                for row in result_orderBadMaterial:
                    if row[4] != Decimal('0.0000'):
                        # print("继续写入")
                        temp_dic["MainpartId"] = row[1]
                        temp_dic["MaterialBadPrice"] = row[4]
                        sumMaterialBadPrice += row[4]
                        orderBadMaterial_dic[row[3]] = temp_dic
                        temp_dic = {}
                # print(orderReceiptMaterial_dic)
            # 判断订单三层表中物料的坏账金额汇总与订单层的订单坏账金额是否一致
            if sumMaterialBadPrice != orderBadPrice:
                msg = "订单{}基础表orderItemDetail中的物料坏账金额汇总与订单层的坏账金额不一致".format(orderNo)
                content = content + msg + '\n'
                # print(msg)
            # 比对两个字典中存放的物料坏账金额是否一致
            # print(badRelateMaterial_dic)
            # print(orderBadMaterial_dic)
            if badRelateMaterial_dic != orderBadMaterial_dic:
                for key in badRelateMaterial_dic.keys():
                    if badRelateMaterial_dic[key] != orderBadMaterial_dic[key]:
                        diff = DeepDiff(badRelateMaterial_dic[key], orderBadMaterial_dic[key])
                        lst = list(diff.values())
                        msg = "订单{}基础表orderItemDetail中的物料坏账金额与坏账JSON中记录的物料坏账金额不一致,物料{}坏账差异详细如下：".format(orderNo,
                                                                                                        key) + str(
                            lst).replace('"', '')
                        content = content + msg + '\n'

    if content != '\n':
        utils.msgPush(robotId, topic, content, username)

#
# if __name__ == '__main__':
#     check_badOrderPrice()
