from utils import utils

webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2242cb88-1c36-4144-a457-2dcafd80bcd1'

#验证车主支付金额与集团实收金额是否一致（包括首次租车金额（押金+租金）、续租金额、结算时需额外支付金额）
def check_receive_money():
    #从配置文件读取ygqn_info相关的参数，以数组的形式返回
    ygqn_info = utils.get_config('ygqn_info')
    topic = '以下订单车主支付金额与集团实收金额不一致，请核对：'
    content=''
    #从租赁记录表获取原订单信息(订单状态:2.处理中；3.已完成；4.已取消；7.已结算;PayState 支付状态 2：部分支付 3：已支付)
    orderSql = 'SELECT  LeaseNumber,PaidMoney  FROM zcp_mallcenter.mall_leaserecord  where deleted=0  and  ChannelType=136 and OrderState in (2,3,4,7) and PayState in (2,3)  '
    orderResult = utils.query_data_mysql('ygqn_database',orderSql)
    if orderResult:
         for record in orderResult:
            orderPayMoney=record[1]
            #根据原订单编号找到原订单、续租订单下的业务类型为租金、押金的应收已支付账单总金额（BillType：1 应收；BillState ：0 未取消；PayState：3 已支付； BusinessType：10.租金 ；11.押金）
            billSql = 'select  sum(PaidMoney)  from  zcp_accountcenter.account_bill   where deleted=0  and BillType =1 and BillState =0  and PayState  =3 and BusinessType in (10,11)  '
            billSql+='  and  OrderNumber   in (select  OrderNumber  from zcp_mallcenter.rel_order where OriginalOrderNumber = \''+str( record[0])+'\' and  Deleted  =0) '
            billResult = utils.query_data_mysql('ygqn_database',billSql)
            if billResult[0][0] is not None:
              billPayMoney=billResult[0][0]
              #查询是否有业务类型为结算的应收已支付账单，获取结算应付金额
              settlementSql =' select  PaidMoney  from  zcp_accountcenter.account_bill   where deleted=0  and BillType =1 and BillState =0  and PayState  =3  '
              settlementSql +=' and BusinessType=12 and OrderNumber = \''+str( record[0])+'\' '
              settlementResult = utils.query_data_mysql('ygqn_database',settlementSql)
              if settlementResult:
                 orderPayMoney=orderPayMoney+settlementResult[0][0] 
                 billPayMoney=billPayMoney+settlementResult[0][0]
              #如果订单的支付金额+结算金额与账单的已支付金额和不一致
              if orderPayMoney != billPayMoney:
                 content = content + '\n' + '订单号：'+ str( record[0]) + '；订单支付金额：'+ str(round(orderPayMoney,2))  + '；应收账单总金额：'+ str(round(billPayMoney,2)) 
              else :
                #找到支付成功的交易流水号（CallBackState 2.回调成功；BusinessType 1.支付；）
                numberSql = ' select  PayBusinessId   from zcp_accountcenter.account_callrecord where CallBackState =2 and BusinessType  =1  and  PayBusinessId  in '
                numberSql +=' (select TransactionNo   from zcp_accountcenter.account_transaction_log where BusinessType  =1 and BillId   in '
                numberSql +=' ( select  id    from  zcp_accountcenter.account_bill   where deleted=0  and BillType =1 and BillState =0  and PayState  =3  and BusinessType in (10,11,12)    and  OrderNumber   in  '
                numberSql +=' (select  OrderNumber  from zcp_mallcenter.rel_order where OriginalOrderNumber = \''+str( record[0])+'\'  and  Deleted  =0))  group by TransactionNo ) group by PayBusinessId  '
                numberResult = utils.query_data_mysql('ygqn_database',numberSql)
                if numberResult:
                   TransactionNo=''
                   for numberRecord in numberResult:
                      TransactionNo = TransactionNo  + ','+ str( numberRecord[0]) 
                   if len(TransactionNo) != 0 :
                     #根据交易号在支付中心数据库查到对应的支付金额（status:2.支付成功）
                     paySql = 'select  sum(Amount)   from  paycenter_fat.payment_order  where  OutOrderNum  in ( '+ TransactionNo[1:len(TransactionNo)] +' )  '
                     paySql += ' and status=2 and  ApplicationId = \''+ ygqn_info['ApplicationId'] +'\' and Deleted =0 '
                     payResult = utils.query_data_mysql('pay_database',paySql)
                     if payResult[0][0] is not None :
                        if orderPayMoney != payResult[0][0]:
                           content = content + '\n' + '订单号：'+ str( record[0]) + '；订单支付金额：'+ str(round(orderPayMoney,2)) + '；应收账单总金额：'+ str(round(billPayMoney,2)) + '；支付中心支付金额：'+ str(round(payResult[0][0],2))
                     else:
                        content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 在支付中心内对应的支付成功的支付信息'
                else:
                    content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 下的应收账单的交易号信息'  
            else:
               content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 对应的符合条件的账单信息'   
         if len(content) != 0:
            utils.msgPush(webhook, topic, content, "")


#验证集团退款金额与车主实收金额是否一致（包括押金退款金额、结算时需额外退款金额）
def check_pay_money():
    #从配置文件读取ygqn_info相关的参数，以数组的形式返回
    ygqn_info = utils.get_config('ygqn_info')
    topic = '以下订单集团退款金额与车主实收金额不一致，请核对：'
    content=''
    #从租赁记录表获取原订单信息(订单状态:3.已完成；4.已取消；7.已结算;PayState 支付状态 2：部分支付 3：已支付)
    orderSql = 'SELECT  LeaseNumber  FROM zcp_mallcenter.mall_leaserecord  where deleted=0  and  ChannelType=136 and OrderState in (3,4,7) and PayState in (2,3) '
    orderResult = utils.query_data_mysql('ygqn_database',orderSql)
    if orderResult:
         for record in orderResult:
            #根据原订单号找到应付账单的支付金额（BillType：2 应付；BillState ：0 未取消；PayState：3 已支付；BusinessType：10.租金 11.押金 12.结算）
            billSql = 'select  sum(PaidMoney)  from  zcp_accountcenter.account_bill   where deleted=0  and BillType =2  and BillState =0  and PayState  =3 and BusinessType in (10,11,12)   and  OrderNumber = \''+str( record[0])+'\' '
            billResult = utils.query_data_mysql('ygqn_database',billSql)
            if billResult[0][0] is not None :
                #找到支付成功的交易流水号（CallBackState 2.回调成功；BusinessType 2.退款；）
                numberSql = 'select  RefundBusinessId    from zcp_accountcenter.account_callrecord where CallBackState =2 and  BusinessType  =2  and ' 
                numberSql += 'RefundBusinessId  in ( select  TransactionNo  from zcp_accountcenter.account_transaction_log where '
                numberSql += 'BusinessType  =2 and OrderNumber=\''+str( record[0])+'\'  group by TransactionNo ) group by RefundBusinessId '
                numberResult = utils.query_data_mysql('ygqn_database',numberSql)
                if numberResult:
                   TransactionNo=''
                   for numberRecord in numberResult:
                      TransactionNo = TransactionNo  + ','+ str( numberRecord[0]) 
                   if len(TransactionNo) != 0 :
                     #根据交易号在支付中心数据库查到对应的支付金额(status 2.退款成功)
                     paySql = 'select   sum(Amount)   from  paycenter_fat.refund_order  where  OutOrderNum  in ( '+ TransactionNo[1:len(TransactionNo)] +' ) '
                     paySql +=' and status=2  and   ApplicationId = \''+ ygqn_info['ApplicationId'] +'\'  and Deleted =0 '
                     payResult = utils.query_data_mysql('pay_database',paySql)
                     if payResult[0][0] is not None :
                        if billResult[0][0] != payResult[0][0]:
                           content = content + '\n' + '订单号：'+ str( record[0]) +  '；应付账单总金额：'+ str(round(billResult[0][0],2)) + '；支付中心退款金额：'+ str(round(payResult[0][0],2))+ '；交易号个数：'+ str(len(numberResult))
                     else:
                        content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 在支付中心内对应的退款成功的退款信息'
                else:
                    content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 下的应付账单的交易号信息'    
         if len(content) != 0:
            utils.msgPush(webhook, topic, content, "")


#获取每个订单下的交易流水号
def get_number():
    #从配置文件读取ygqn_info相关的参数，以数组的形式返回
    ygqn_info = utils.get_config('ygqn_info')
    topic = '获取订单下的交易流水号，请查收：'
    content=''
    #从租赁记录表获取原订单信息(订单状态:3.已完成；4.已取消；7.已结算;PayState 支付状态 2：部分支付 3：已支付)
    orderSql = 'SELECT  LeaseNumber  FROM zcp_mallcenter.mall_leaserecord  where deleted=0  and  ChannelType=136 and OrderState in (3,4,7) and PayState in (2,3)  '
    orderResult = utils.query_data_mysql('ygqn_database',orderSql)
    if orderResult:
         for record in orderResult:
            #根据原订单号找到应付账单的支付金额（BillType：2 应付；BillState ：0 未取消；PayState：3 已支付；BusinessType：10.租金 11.押金 12.结算）
            billSql = 'select  sum(PaidMoney)  from  zcp_accountcenter.account_bill   where deleted=0  and BillType =2  and BillState =0  and PayState  =3 and BusinessType in (10,11,12)   and  OrderNumber = \''+str( record[0])+'\' '
            billResult = utils.query_data_mysql('ygqn_database',billSql)
            if billResult[0][0] is not None :
                #找到支付成功的交易流水号（CallBackState 2.回调成功；BusinessType 2.退款；）
                numberSql = 'select  RefundBusinessId    from zcp_accountcenter.account_callrecord where CallBackState =2 and  BusinessType  =2  and ' 
                numberSql += 'RefundBusinessId  in ( select  TransactionNo  from zcp_accountcenter.account_transaction_log where '
                numberSql += 'BusinessType  =2 and OrderNumber=\''+str( record[0])+'\'  group by TransactionNo ) group by RefundBusinessId '
                numberResult = utils.query_data_mysql('ygqn_database',numberSql)
                if numberResult:
                   #导出具体的交易流水号：
                   '''TransactionNo=''
                   for numberRecord in numberResult:
                      TransactionNo = TransactionNo  + ';'+ str( numberRecord[0]) 
                   if len(TransactionNo) != 0 :
                     content = content + '\n' + '订单号：'+ str( record[0]) +  '；交易流水号：'+ str(TransactionNo[1:len(TransactionNo)]) '''
                   #导出订单对应的交易流水号的个数
                   content = content + '\n' + '订单号：'+ str( record[0]) +  '；交易流水号个数：'+ str(len(numberResult)) 
                else:
                    content = content + '\n' + '未找到订单号：'+ str( record[0]) + ' 下的应付账单的交易号信息'    
         if len(content) != 0:
            utils.msgPush(webhook, topic, content, "")