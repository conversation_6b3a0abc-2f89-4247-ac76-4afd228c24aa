from utils import utils
import datetime

currentDate = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2242cb88-1c36-4144-a457-2dcafd80bcd1'

#验证已支付的订单是否都已发放卡券（PayState 支付状态 2：部分支付 3：已支付）
def check_card_exist():
    sql = 'SELECT LeaseNumber FROM zcp_mallcenter.mall_leaserecord where deleted=0  and  ChannelType=136 and PayState in (2,3) and  OriginalOrderId  not in  '
    sql+=' (select ProviderOrderId   from zcs_membercenter.tb_coupon_record  where ChannelType=136 and Deleted =0 and  ProviderOrderId is not null  )'
    result = utils.query_data_mysql('ygqn_database',sql)
    if result:
        topic = '以下订单已支付但未生成卡券，请核对：'
        content = ''
        for record in result:
            if record[0]:
                content = content + '\n' + str(record[0]) 
        if len(content) != 0:
            utils.msgPush(webhook, topic, content, "")


#验证卡券生效状态与失效时间是否匹配
def check_card_status():
    sql = 'select a.EffectAt,a.InvalidAt,a.EffectState,a.ProviderOrderId,b.OrderState,b.LeaseNumber,a.CouponNumber  from zcs_membercenter.tb_coupon_record as a '
    sql+=' left join zcp_mallcenter.mall_leaserecord as b on a.ProviderOrderId=b.OriginalOrderId '
    sql+=' where a.ChannelType=136 and a.Deleted =0  and b.deleted=0  and  b.ChannelType=136 and a.CouponNumber="6F065892188A"   order by b.OrderState  asc'
    result = utils.query_data_mysql('ygqn_database',sql)
    if result:
        topic = '以下卡券的生效状态错误，请核对：'
        content = ''
        for record in result:
            #获取每个卡券在页面显示的状态
            webStatus = get_web_status(str(record[6]))
            print("数据库:"+str(record[2]))
            print("页面："+str(webStatus))
            # 订单状态 4 已取消 此时 卡券状态为 4 已作废
            if record[4] == 4 :
                if record[2] != 4 and  webStatus !=4  :
                    content = content + '\n' +'  卡券编码：'+ str(record[6])+'  对应的订单编号：'+ str(record[5])  
            else:
                dt1= record[0].strftime("%Y-%m-%d %H:%M:%S")
                dt2= record[1].strftime("%Y-%m-%d %H:%M:%S")
                #如果当前时间在生效时间范围内，此时卡券状态应为2 表示生效中
                if  dt1 <= currentDate <= dt2 : 
                    if record[2] != 2 and  webStatus !=2 :
                        content = content + '\n' +'  卡券编码：'+ str(record[6])+'  对应的订单编号：'+ str(record[5])  
                else:
                    if dt1 > currentDate:
                      # #如果生效时间>当前时间，此时卡券状态为未生效 1
                      if record[2] != 1 and  webStatus !=1 :
                        content = content + '\n' +'  卡券编码：'+ str(record[6])+'  对应的订单编号：'+ str(record[5])  
                    if dt2 < currentDate:   
                      #如果失效时间>当前时间，此时卡券状态为已失效 3
                      if record[2] != 3 and  webStatus !=3 :
                        content = content + '\n' +'  卡券编码：'+ str(record[6])+'  对应的订单编号：'+ str(record[5]) 
        if len(content) != 0:
            utils.msgPush(webhook, topic, content, "")


#根据卡券id调用接口获取接口返回的生效状态信息
def get_web_status(number):
    headers = utils.get_indentity_implicit_auth('ygqn_web')
    result=utils.call('ygqn_web','/api/v1/coupon/querycoupon?pageSize=10&pageIndex=1&filters[0].field=couponNumber&filters[0].op=cn&filters[0].term=C651DB55EF69&filter.op=and&filter.rules[0].field=couponNumber&filter.rules[0].op=cn&filter.rules[0].data='+number+'&filter.rules[1].field=effectState&filter.rules[1].op=eq&filter.rules[2].field=verifiedState&filter.rules[2].op=eq&filter.rules[3].field=couponName&filter.rules[3].op=cn&filter.rules[4].field=custName&filter.rules[4].op=cn&filter.rules[5].field=telNumber&filter.rules[5].op=cn&filter.rules[6].field=effectAt&filter.rules[6].op=bt&filter.rules[6].datas[0]=&filter.rules[6].datas[1]=&filter.rules[7].field=invalidAt&filter.rules[7].op=bt&filter.rules[7].datas[0]=&filter.rules[7].datas[1]=&filter.rules[8].field=recentVerifiedAt&filter.rules[8].op=bt&filter.rules[8].datas[0]=&filter.rules[8].datas[1]=&filter.rules[9].field=channelType&filter.rules[9].op=eq','GET',headers)
    status=''
    if result:
        status=utils.json_path(result['body'],"$.data[0].effectState")[0]
    return status