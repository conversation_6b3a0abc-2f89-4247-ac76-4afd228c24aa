import datetime
from utils import utils


def test_stickerDataNotify():

    #昨日绑定数量
    sql_yesterdayBindCount = "select count(1) from zcs_center.tb_qr_code WHERE BindState = 1 AND TO_DAYS(NOW()) - TO_DAYS(UpdatedAt) = 1"
    #昨日拨打次数
    sql_yesterdayCallCount = "select count(id) FROM zcp_logcenter.log_conversation WHERE TO_DAYS(NOW()) - TO_DAYS(CreatedAt) = 1"
    #昨日通话次数
    sql_yesterdayCalledCount = "select count(id) FROM zcp_logcenter.log_conversation WHERE json_unquote(json_extract(CallData,'$.Duration')) <> 0 AND TO_DAYS(NOW()) - TO_DAYS(CreatedAt) = 1"
    #昨日通话时长
    sql_yesterdayCalledDuration = "SELECT sum(json_unquote(json_extract(CallData,'$.Duration'))) FROM zcp_logcenter.log_conversation WHERE TO_DAYS(NOW()) - TO_DAYS(CreatedAt) = 1"
    #累计绑定数量
    sql_totalBindCount = "select count(id) from zcs_center.tb_qr_code WHERE BindState = 1 and UpdatedAt <= DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00')"
    #累计拨打次数
    sql_totalCallCount = "select count(id) FROM zcp_logcenter.log_conversation"
    #累计通话次数
    sql_totalCalledCount = "select count(id) FROM zcp_logcenter.log_conversation WHERE json_unquote(json_extract(CallData,'$.Duration')) <> 0 "
    #累计通话时长
    sql_totalCalledDuration = "SELECT sum(json_unquote(json_extract(CallData,'$.Duration'))) FROM zcp_logcenter.log_conversation"

    #累计生成数量
    sql_totalCount = "select count(1) from zcs_center.tb_qr_code"
    #对接客户数量
    sql_totalCustCount = "select count(DISTINCT DepartId) from zcs_center.tb_qr_code"


    #昨日绑定数量
    yesterdayBindCount = utils.query_data_mysql('lt_database',sql_yesterdayBindCount)
    #昨日拨打次数
    yesterdayCallCount = utils.query_data_mysql('lt_database',sql_yesterdayCallCount)
    #昨日通话次数
    yesterdayCalledCount = utils.query_data_mysql('lt_database',sql_yesterdayCalledCount)
    #昨日通话时长
    yesterdayCalledDuration = utils.query_data_mysql('lt_database',sql_yesterdayCalledDuration)
    print(yesterdayCalledDuration[0][0])
    if yesterdayCalledDuration[0][0]:
        yesterdayCalledDuration = round(yesterdayCalledDuration[0][0]/60, 2)
    else:
        yesterdayCalledDuration = '0.00'

    #累计绑定数量
    totalBindCount = utils.query_data_mysql('lt_database',sql_totalBindCount)
    #累计拨打次数
    totalCallCount = utils.query_data_mysql('lt_database',sql_totalCallCount)
    #累计通话次数
    totalCalledCount = utils.query_data_mysql('lt_database',sql_totalCalledCount)
    #累计通话时长
    totalCalledDuration = utils.query_data_mysql('lt_database',sql_totalCalledDuration)
    totalCalledDuration = round(totalCalledDuration[0][0]/60, 2)


    #累计生成数量
    totalCount = utils.query_data_mysql('lt_database',sql_totalCount)
    #对接客户数量
    totalCustCount = utils.query_data_mysql('lt_database',sql_totalCustCount)

    endAt = datetime.date.today().strftime("%Y-%m-%d %H:%M:%S")
    beginAt = (datetime.date.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d %H:%M:%S")

    # 昨日按钮点击次数
    headers = utils.get_indentity_password_auth('ApiPlat')
    result = utils.call('ApiPlat','/api/v1/logs/zhongrui/details/count?pageSize=10&pageIndex=1&key=SensitiveFields&appKey=tYiMojoR3ItrfWrn4Jed&productTag=axb&beginAt='+beginAt+'&endAt='+endAt, 'GET', headers)
    yesterClickCount = utils.json_path(result['body'], '$.count')

    topic_yesterday = '挪车码昨日数据统计\n'
    topic_total = '挪车码累计数据统计\n'
    warning_yesterday = '\n昨日绑定数量：'+str(yesterdayBindCount[0][0])+'\n昨日点击次数：'+str(yesterClickCount[0])+'\n昨日拨号次数：'+str(yesterdayCallCount[0][0])+'\n昨日通话次数：'+str(yesterdayCalledCount[0][0])+'\n昨日通话时长：'+str(yesterdayCalledDuration)+'分钟'
    warning_total = '\n累计绑定数量：'+str(totalBindCount[0][0])+'\n累计拨号次数：'+str(totalCallCount[0][0])+'\n累计通话次数：'+str(totalCalledCount[0][0])+'\n累计通话时长：'+str(totalCalledDuration)+'分钟\n累计生成数量：'+str(totalCount[0][0])+'\n对接客户数：'+str(totalCustCount[0][0])
    username = ''
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ee6a11b5-1fd3-4a37-af18-ca2d4caefe23'
    #webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
    utils.msgPush(webhook, topic_yesterday, warning_yesterday, username)
    utils.msgPush(webhook, topic_total, warning_total, username)