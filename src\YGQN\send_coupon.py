from utils import utils
from datetime import datetime

def send_coupon():

    sql = '''select 
                a.Id,
                a.<PERSON>,
                d<PERSON>,
                d.<PERSON>,
                c.<PERSON>,
                a.PlanLease<PERSON>egin<PERSON>t , 
                a.<PERSON>t,
                d.<PERSON>,
                d.ZcpCustId
            from `zcp_mallcenter`.`mall_leaserecord` a 
                inner join `zcp_mallcenter`.`mall_providerorder`  b 
                on a.OriginalOrderId = b.Id
                and b.`Deleted` = 0
                inner join `zcp_custcenter`.`cust_providerinfo` c
                on b.`ProviderId` = c.Id
                and c.`Deleted` = 0
                inner JOIN `zcs_center`.`tb_userinfo`  d 
                on b.`CustId` = d.`ZcpCustId` 
                and d.`Deleted` = 0
            where a.`OrderState` = 2   -- 订单状态为处理中
                and a.`PayState` in (2,3) -- 支付状态为已支付/部分支付
                and a.`DeliverState`=20   -- 已交车
                and CONCAT(DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY),' 00:00:00') <= a.PlanLeaseEndAt  -- 7天要到期
                and CONCAT(DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY),' 23:59:59') >= a.PlanLeaseEndAt
                and a.`ChannelType` = '136' -- 渠道是阳氢'''
    results = utils.query_data_mysql('lt_database', sql)
    
    # 获取优惠券id
    coupons = utils.get_config('ygqn_coupon')
    # 获取接口token
    headers = utils.get_indentity_implicit_auth('ygqn_web')
    # 初始化log文件
    logPath = 'src\YGQN\coupon.log'
    logger = utils.log(logPath)

    today = datetime.now().strftime("%Y-%m-%d")
    passCount = 0
    failCount = 0


    for order in results:
        # 计算租期
        period = ((order[6] - order[5]).days // 30)
        # 租期为1个月发10元券，2个月发20元券，3个月及以上发30元券
        if period == 1 or period == 0:
            coupon = coupons['coupon10']
        elif period == 2:
            coupon = coupons['coupon20']
        else:
            coupon = coupons['coupon30']
        # 拼接发券接口参数，调用发券接口
        param = {"messageType": 7,"couponList": [{"couponId": coupon,"couponCount": 1}],"custsForIssue": [{"linkmanTel": order[3],"linkmanName": order[2],"userCenterId": order[7],"departId": order[4],"zcpCustId": order[8],"id": None,"remark": "系统自动发放"}]}
        response = utils.call(alias='ygqn_web', path='/api/v1/coupon/issueCoupons2B', method='POST', param=param, headers=headers)
        # 统计接口成功和失败的数量
        if response['code'] != 200:
            failCount += 1
            logger.error('状态码：%s 订单信息：%s 接口参数：%s', response['code'], order, param)
        else:
            passCount += 1
            logger.info('状态码：%s 订单信息：%s 接口参数：%s', response['code'], order, param)
        # 记录log，便于排查失败原因

    # 把发券情况推送到群里
    topic = today+' 优惠券发放完成\n'
    content = '\n成功：'+ str(passCount) + '   失败：'+ str(failCount)
    webhook = coupons['robotId']
    fileKey = coupons['fileKey']
    utils.msgPush(topic=topic, content=content, webhook=webhook)
    # 如果发券了的话，把log文件推到群里
    if results:
        utils.msgPush_file(fileKey, webhook, logPath)