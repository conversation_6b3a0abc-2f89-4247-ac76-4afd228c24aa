from utils import utils
import datetime

webhook_sk = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0d7282cf-31c1-4bd5-aaf8-f7fdbdcaddc4'
webhook_zr = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0d187900-3708-4bf6-8862-799da6f4c706'
webhook_pso = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8b7e9223-53a5-47a6-9891-493ebac4815f'

def dailyTaskNotify():
    topic = '日志填写提醒\n'
    username = '@all'
    content = '\n请大家及时填写本周工作日志。'
    utils.msgPush(webhook_sk, topic, content, username)
    utils.msgPush(webhook_zr, topic, content, username)

def psoNotify():
    topic = '例行检查提醒\n'
    username = '@all'
    content = '\n1、更新《研发中心客制化项目汇总表》\n2、检查项目任务类别、业务线、任务版本 、标签、所属发布\n3、更新地书项目阶段'
    utils.msgPush(webhook_pso, topic, content, username)

def monthTaskNotify():
    now = datetime.datetime.now()
    weekday = now.weekday()
    if weekday in range(0,5):
        topic = '日志填写提醒\n'
        username = '@all'
        content = '\n临近月底，请大家务必在次月1号之前将本月工时填写完整！'
        utils.msgPush(webhook_zr, topic, content, username)

    