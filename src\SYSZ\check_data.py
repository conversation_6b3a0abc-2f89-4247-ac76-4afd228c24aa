from utils import utils
from datetime import datetime

webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e1d298ec-63c7-4c4b-bc3c-66d13f9ae484'
username = ''
date = datetime.now().strftime("%Y-%m-%d")

# 已投放数量验证
def data_summary():
    sql_invested = "select A.Id  as '券ID', A.`TicketName` as '券名称', A.ValidDateEnd as '过期时间', A.GoodsgroupName '商品组合名称', count(1) as '已投放券数量' from tb_ticketinfo A left join `tb_ticketcode` B ON A.Id = B.ticketId and B.Deleted = 0 where A.Deleted = 0 and A.ValidDateEnd >= '"+date+"' group by A.Id order by A.Id"
    sql_exchanged = "select A.Id  as '券ID', A.`TicketName` as '券名称', A.ValidDateEnd as '过期时间', A.Goodsgroup<PERSON>ame '商品组合名称', count(1) as '已投放券数量' from tb_ticketinfo A left join `tb_ticketcode` B ON A.Id = B.ticketId and B.Deleted = 0 and B.Status = 4 WHERE A.Deleted = 0 and A.ValidDateEnd >= '"+date+"' group by A.Id order by A.Id"

    invested = utils.query_data_mysql('sysz_database', sql_invested)
    exchanged = utils.query_data_mysql('sysz_database', sql_exchanged)

    if invested and exchanged:
        summary = zip(invested, exchanged)
        topic = '卡券兑换明细汇总\n'
        content = '\n'
        for i in summary:
            content = content +'【'+ i[0][1] +'】已投放 '+ str(i[0][4]) +' 张，已兑换 '+ str(i[1][4]) + ' 张\n'
        utils.msgPush(webhook,topic,content,username)

def exception_summary():
    sql_sendList = "select A.Id,count(1) as '发货单数量' from `tb_ticketcode` A join tb_ticketusingrecord B ON A.Id=B.`TicketCodeId` and B.Deleted=0 join tb_ticketusingdetail C ON B.Id=C.UsingRecordId and C.Deleted=0 join `tb_expressinfo`  D ON C.Id=D.FKId WHERE  A.`Deleted` =0 and A.ValidDateEnd >= '"+date+"' group by A.id  having 发货单数量 > 1 order by 2 Desc"
    sql_couponList = "select a.Id as '券Id', a.TicketName as '券名称', b.OnwerPhone as '手机号', count(1) as '数量' from `tb_ticketinfo` a join `tb_ticketcode` b on a.`Id` =b.`TicketId` where a.deleted=0 and b.deleted=0 and length(b.OnwerPhone) >6 and a.ValidDateEnd >='"+date+"' group by a.Id,b.OnwerPhone having 数量 > 10"
    sql_package_phone = "select D.ReceiverPhone as '收件人电话', count(1) as '快递数', GROUP_CONCAT(C.GoodsgroupName) '商品组合名称' from `tb_ticketcode` A join tb_ticketusingrecord B ON A.Id=B.`TicketCodeId` and B.Deleted=0 join tb_ticketusingdetail C ON B.Id=C.UsingRecordId and C.Deleted=0 join `tb_expressinfo`  D ON C.Id=D.FKId WHERE  A.`Deleted` =0 and A.ValidDateEnd>= '"+date+"' group by D.ReceiverPhone having 快递数 > 10"
    sql_package_address = "select D.ReceiverAddress as '收件地址', count(1) as '快递数', GROUP_CONCAT(C.GoodsgroupName) '商品组合名称' from `tb_ticketcode` A join tb_ticketusingrecord B ON A.Id=B.`TicketCodeId` and B.Deleted=0 join tb_ticketusingdetail C ON B.Id=C.UsingRecordId and C.Deleted=0 join `tb_expressinfo`  D ON C.Id=D.FKId WHERE  A.`Deleted` =0 and A.ValidDateEnd>= '"+date+"'  group by D.ReceiverAddress having 快递数 > 10"

    sendList = utils.query_data_mysql('sysz_database', sql_sendList)
    couponList = utils.query_data_mysql('sysz_database', sql_couponList)
    package_address = utils.query_data_mysql('sysz_database', sql_package_address)
    package_phone = utils.query_data_mysql('sysz_database', sql_package_phone)

    if sendList:
        topic = "以下卡券存在多条发货单数据，请确认是否异常：\n"
        content = "\n"
        for i in sendList:
            content = content + i[0] +"，发货单数量："+ str(i[1]) + "\n"
        utils.msgPush(webhook, topic, content, username)

    if couponList:
        topic = "以下手机号领取了多张相同的卡券，请确认是否异常：\n"
        content = "\n"
        for i in couponList:
            content = content + i[2] + " 已领取【" + i[0]+i[1] + "】" + str(i[3]) + "张\n"
        utils.msgPush(webhook, topic, content, username)
    
    if package_address:
        topic = "以下地址存在多个发货单，请确认是否异常：\n"
        content = "\n"
        for i in package_address:
            content = content + "地址【" + i[0] + "】, 快递数：" + str(i[1]) + "\n"
        utils.msgPush(webhook, topic, content, username)
            
    if package_phone:
        topic = "以下手机号存在多个发货单，请确认是否异常：\n"
        content = "\n"
        for i in package_phone:
            content = content + "手机号【" + i[0] + "】, 快递数：" + str(i[1]) + "\n"
        utils.msgPush(webhook, topic, content, username)