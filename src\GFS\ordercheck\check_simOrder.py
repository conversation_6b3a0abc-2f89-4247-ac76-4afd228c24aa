# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
# 定义全局变量
import datetime

from utils import utils
from utils.mysqlOp import MysqlOp
from utils.sqlServerOp import SqlServerOp

today = datetime.date.today()
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday.strftime("%Y-%m-%d")
start_date = str(yesterday) + ' 00:00:00'
end_date = str(today) + ' 00:00:00'


def check_simOrder():
    robotId = utils.get_config('gfs_report')['robotId']
    username = '18661922397'
    topic = '昨日新增SIM卡相关订单与订单中心对比差异如下，请核实:'
    content = '\n'
    # 定义变量
    sim_list = []
    mall_sim_list = []
    lunz_list = []
    lunz_dic = {}
    lunz_payTradeNo = []
    mall_lunz_list = []
    # 实例数据库对象
    dbinfo_mall = MysqlOp('mall_database')
    conn_mall = dbinfo_mall.getConn()
    dbinfo_sim = MysqlOp('sim_database')
    conn_sim = dbinfo_sim.getConn()
    dbinfo_lunz = SqlServerOp('lunz_database')
    conn_lunz = dbinfo_lunz.getConn()
    # 1.对比SIM卡中心生成和订单中心接收的订单
    # SIM卡中心推送给订单中心的订单
    # 正式环境
    # sql_sim = '''  select OrderId  from tb_simmarkinfo ts
    #    where MarkType in (3,5,9)
    #    and OrderId !=''
    #    and UpdatedAt  >='{0}'
    #    and UpdatedAt <'{1}'
    #    '''.format(start_date, end_date)
    # 测试环境
    sql_sim = '''  select OrderId  from tb_simmarkinfo ts  
    where MarkType in (3,5,9)
    and OrderId !=''
    and UpdatedAt  >='2024-01-12 00:00:00'
    and UpdatedAt <'2024-01-13 00:00:00' 
    '''
    # 订单中心接收的SIM订单
    # 正式环境
    # sql_mall = '''select id from tb_orderinfo
    #     where SourceType =2
    #     and OrderType  in (8,9,11,12)
    #     and AuditTime >='{0}'
    #     and AuditTime <'{1}'
    #     '''.format(start_date, end_date)
    # 测试环境
    sql_mall_sim = '''select id from tb_orderinfo 
    where SourceType =2
    and OrderType  in (8,9,11,12)
    and AuditTime >='2024-01-12 00:00:00'
    and AuditTime <'2024-01-13 00:00:00'
    '''
    result_sim = utils.select(conn_sim, sql_sim)
    result_mall_sim = utils.select(conn_mall, sql_mall_sim)
    if result_sim:
        for row in result_sim:
            sim_list.append(row[0])
    if result_mall_sim:
        for row in result_mall_sim:
            mall_sim_list.append(row[0])
    print(sim_list)
    print(mall_sim_list)
    if len(sim_list) != len(mall_sim_list):
        diff1 = compare_arrays(mall_sim_list, sim_list)
        if diff1:
            msg = '订单ID{}订单中心已生成，但是在SIM中心回调失败'.format(diff1)
            content = content + msg + '\n'
        diff2 = compare_arrays(sim_list, mall_sim_list)
        if diff2:
            msg = '订单ID{}SIM中心已生成，但是推送订单中心失败'.format(diff2)
            content = content + msg + '\n'
    # 2.对比轮子管家生成订单和订单中心接收的订单
    # 轮子管家产生的订单
    # 正式环境
    # sql_lunz = '''select PayOutTradeNo ,PayTradeNo  from wechatvps.dbo.Membership_Payment
    # where Deleted =0
    # and CreatedAt  >='{0}'
    # and CreatedAt <'{1}'
    # and Status =1
    # '''.format(start_date, end_date)
    # 测试环境
    sql_lunz = '''select PayOutTradeNo ,PayTradeNo  from wechatvps.dbo.Membership_Payment 
    where Deleted =0
    and CreatedAt  >='2024-01-12 00:00:00'
    and CreatedAt <'2024-01-13 00:00:00'
    and Status =1
    '''
    # 订单中心接收的轮子管家的订单
    # 正式环境
    # sql_mall_lunz = '''select id,appcode  from tb_orderinfo
    #     where SourceType =3
    #     and OrderType  in (11,12)
    #     and AuditTime >='{0}'
    #     and AuditTime <'{1}'
    #     '''.format(start_date, end_date)
    # 测试环境
    sql_mall_lunz = '''select id,appcode from tb_orderinfo 
    where SourceType =3
    and OrderType  in (11,12)
    and AuditTime >='2024-01-12 00:00:00'
    and AuditTime <'2024-01-13 00:00:00'
    '''
    result_lunz = utils.select(conn_lunz, sql_lunz)
    result_mall_lunz = utils.select(conn_mall, sql_mall_lunz)
    if result_lunz:
        for row in result_lunz:
            lunz_dic[row[0]] = row[1]
            lunz_list.append(lunz_dic)
            lunz_dic = {}
    if result_mall_lunz:
        for row in result_mall_lunz:
            mall_lunz_list.append(row[1])
    print(lunz_list)
    print(mall_lunz_list)
    # if len(lunz_list) != len(mall_lunz_list):
    if 1:
        for item in lunz_list:
            for key in item.keys():
                if 'a' in key:
                    sql_temp = '''select PayTradeNo  from wechatvps.dbo.Membership_Payment  WHERE PayOutTradeNo ='{}'
                    '''.format(key[:-1])
                    print(sql_temp)
                    result_temp = utils.select(conn_lunz, sql_temp)
                    if result_temp:
                        item[key] = result_temp[0][0] + 'a'
                        lunz_payTradeNo.append(item[key])
                else:
                    lunz_payTradeNo.append(item[key])
        print(lunz_list)
        print(lunz_payTradeNo)
        diff3 = compare_arrays(lunz_payTradeNo, mall_lunz_list)
        if diff3:
            msg = '工单{}所对应的订单在轮子管家已生成，但是推送订单中心失败'.format(diff3)
            content = content + msg + '\n'
        diff4 = compare_arrays(mall_lunz_list, lunz_payTradeNo)
        if diff4:
            msg = '工单{}所对应的订单已推送至订单中心，但是轮子管家回调失败'.format(diff4)
            content = content + msg + '\n'
    if content != '\n':
        print(content)

        # utils.msgPush(robotId, topic, content, username)
    conn_sim.close()
    conn_lunz.close()
    conn_mall.close()

# 定义数组差异比对方法
def compare_arrays(arr1, arr2):
    diff = [val for val in arr1 if val not in arr2]
    return diff


if __name__ == '__main__':
    check_simOrder()
