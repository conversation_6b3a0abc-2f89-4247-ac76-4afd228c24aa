import requests
from bs4 import BeautifulSoup
from utils import utils
from datetime import datetime
from decimal import Decimal

def get_rate():
    url = "http://www.boc.cn/sourcedb/whpj/"
    res = requests.get(url)
    res.encoding = 'utf-8'
    soup = BeautifulSoup(res.text, 'html.parser')
    table = soup.find_all('table')[1]
    trs = table.find_all('tr')
    rate = []
    for tr in trs[1:]:
        tds = tr.find_all('td')
        currency = tds[0].text
        if currency == '日元':
            cash_buy = tds[5].text
            date = tds[6].text
            break

    cny = Decimal(cash_buy)*220
    topic = '<font color=\"warning\">'+date+'</font> 22000块日元能换 **<font color=\"info\">'+str(cny)+'</font>** 块人民币'

    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=287773a0-0cb9-4aae-b1df-8aada870828f'
    utils.msgPush_markdown(webhook=webhook, topic=topic, content='')