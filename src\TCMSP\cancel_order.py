from utils import utils
from datetime import datetime

def cancel_order():
    """
    当日未签到的挂号记录，次日零点取消
    """
    # 查询当日未签到的挂号记录，限制结果为10条记录
    sql = "SELECT BusinessId FROM zyy_goodscenter.vi_ap_appointment where AppointmentState=1 and AppointmentDate < CURDATE()"
    
    # 执行SQL查询，获取结果
    result = utils.query_data_mysql('tcmsp_database', sql)
    # 初始化成功和失败的取消操作次数
    passCount = 0
    failCount = 0
    # 获取当前日期并格式化为YYYY-MM-DD的字符串形式
    today = datetime.now().strftime("%Y-%m-%d")
    # 获取名为tcmsp_info的配置信息
    config = utils.get_config('tcmsp_info')

    # 如果查询结果不为空
    if result:
        # 获取tcmsp_web的身份验证头信息

        headers = utils.get_indentity_implicit_auth('tcmsp_web', 'indentity_tcmsp')# 测试环境调试时要把indentity_tcmsp删掉
        # 初始化日志文件，指定日志文件路径为src\TCMSP\order.log，并创建一个日志记录器logger
        logPath = 'src\TCMSP\order.log'
        logger = utils.log(logPath)
        # 遍历查询结果中的每一条记录
        for item in result:
            # 提取BusinessId并格式化为请求参数param
            businessid = item[0]            
            param = 'orderId={}&cancelBody=3&orderType=1&WorkFlowChannelType=1&cancelReasonType=DT0000004691'.format(businessid)
            # 调用utils模块中的call函数，发送GET请求到tcmsp_web的/api/v1/mallcustorder/CancleOrder接口，传递param和headers
            result = utils.call('tcmsp_web', '/api/v1/mallcustorder/CancleOrder?'+param, headers=headers, method='GET')
            # 根据返回结果的状态码（code）判断取消操作是否成功
            if result['code'] != 200:
                # 如果状态码不为200，则增加failCount，并记录错误日志
                failCount += 1
                logger.error('状态码：%s 接口参数：%s 返回值：%s', result['code'], param, result['body'])
            else:
                # 如果状态码为200，则增加passCount，并记录成功日志
                passCount += 1
                logger.info('状态码：%s 接口参数：%s 返回值：%s', result['code'], param, result['body'])
        
    # 构建通知内容
    topic = today+' 挂号记录取消结果\n'
    content = '\n成功：'+ str(passCount) + '   失败：'+ str(failCount)
    # 获取Webhook和文件Key
    webhook = config['robotId']
    fileKey = config['fileKey']
    # 发送通知到指定的Webhook
    utils.msgPush(topic=topic, content=content, webhook=webhook)
    # 如果有结果，则上传日志文件
    if result:
        utils.msgPush_file(fileKey, webhook, logPath)

