import time
from collections import Counter
from utils import utils

projectList_zr = [
                #模友
                #['63f759ae62ece1b6356f090b','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'],
                #智车商
                #['63f75d1362ece1b6356f0f53','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'],
                #阳光氢能
                #['63fec9bb2949f8258ff4880a','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'],
                #集团业财中心
                ['63f756b731aa0a3238b999f5','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dcc0f42f-91d2-4d89-89a7-fced2c4e88e5'],
                #创格融资租赁买卖车
                ['64bf248746c2f426f8218218','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=87eb9cba-31dd-4ef6-91b3-8161e2d6c93d'],
                #东莞药价查询服务系统
                ['6420f1a04a65f65c8779ea3a','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bf1f61b2-14ab-4871-8a01-8d949aef3189'],
                #馨之园榴莲信息化-客户定制化项目
                ['666bfddb77fd935aeff7cb3a','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bb755dbe-6faa-4fd3-a3a4-0481be4f0e18']
                ]

projectList_sk = [
                 #合同管理系统
                ['64abc3fc7bc6f739a762b81c','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7f65b2ab-2ffc-43ae-91c7-4d88c3daf5d5'],
                # 投资项目
                ['64a905e27bc6f739a7611239','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9295c9db-f732-455a-bfac-ccff24798af3'],
                # 领导驾驶舱
                ['64a90e357bc6f739a76112a3','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=08305796-8d36-4b96-b3bd-cc6905486ef0'],
                # 运营公司考勤系统
                ['64a8df827bc6f739a7610f74','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4d32474b-4fbc-47cf-8e65-58869b44a4f4'],
                # 置业公司考勤系统
                ['64ab5cfd1c86cd9153c484c4','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6d8064ec-4387-41dd-b9f4-6bc789e4423c'],
                # 设计协同
                #['64a8fa557bc6f739a76111d7','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2fb0e8b8-5392-4a58-8531-5d1c3d90e027'],
                # 决策分析系统PC端
                ['6539fce200d17921c18cfba1','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=08305796-8d36-4b96-b3bd-cc6905486ef0'],
                # 资产管理系统
                ['64abc3001c86cd9153c48a97','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0854dd62-8b52-41bb-aade-f818a70efb9d'],
                # 物资管理系统
                ['64abc38a7bc6f739a762b7f0','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de155523-e9dd-42c4-8e02-b1544b1f3036'],
                # 企业门户
                ['64ae055a864745cdaacf902d','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a7633aef-b881-43b5-b4fe-14939a9b84da'],
                # 青岛地铁人力资源数字化平台二期
                ['666f9c7f2ef79dbee8af8570','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=72f1a7ac-c713-483b-8ea8-bec7d8f62a39'],
                # 数据资源中心
                ['66583098d082dd4c9103adee','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cef568f-ac33-4809-b493-1a3d7f3fb7ed'],
                # 数科院后勤服务系统
                ['67187141e58a938c00c82a4f','https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4505c409-0874-475a-a967-977632f61f31']
                 ]

def test_bugDailyReport_sk():

    headers = utils.get_pingcode_auth('PingCode_sk')
    for project in projectList_sk:
        projectId = project[0]
        webhook = project[1]
        bugList = []
        activeList = []
        solveList = []
        bugList = []
        projectName = None
        activeResults = utils.call('PingCode_sk', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=646c55a523823350548b26c1,643614b10a0d63ab9fd81a69,646c562a6f57855c4326b85c&page_size=100','GET', headers=headers)
        total = utils.json_path(activeResults['body'], '$.total')
        if total[0]:
            page = 1
            activeList = utils.json_path(activeResults['body'], '$.values..assignee.display_name')
            pageTotal = int(total[0])//100
            projectName = utils.json_path(activeResults['body'], '$.values..project.name')[0]
            while(page != 0 and page <= pageTotal):
                activeResults = utils.call('PingCode_sk', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=646c55a523823350548b26c1,643614b10a0d63ab9fd81a69,646c562a6f57855c4326b85c&page_size=100&page_index='+str(page),'GET', headers=headers)
                num = utils.json_path(activeResults['body'], '$.values..assignee.display_name')
                activeList.extend(num)
                page = page+1
        solveResults = utils.call('PingCode_sk', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=646c55fa13cb3d13540ec952,646c560fff34a04faacd345c&page_size=100','GET', headers=headers)
        total = utils.json_path(solveResults['body'], '$.total')
        if total[0]:
            page = 1
            if projectName is None:
                projectName = utils.json_path(solveResults['body'], '$.values..project.name')[0]
            solveList = utils.json_path(solveResults['body'], '$.values..created_by.display_name')
            pageTotal = int(total[0])//100
            while(page != 0 and page <= pageTotal):
                solveResults = utils.call('PingCode_sk', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=646c55fa13cb3d13540ec952,646c560fff34a04faacd345c&page_size=100&page_index='+str(page),'GET', headers=headers)
                num = utils.json_path(solveResults['body'], '$.values..created_by.display_name')
                solveList.extend(num)
                page = page+1
        bugList.extend(activeList)
        bugList.extend(solveList)
        if bugList:
            bugList = Counter(bugList)
            topic = '【'+projectName+'】项目当前缺陷情况如下：\n待修复缺陷：'+str(len(activeList))+'\n待验证缺陷：'+str(len(solveList))+'\n'
            content = '\n缺陷分布情况如下：\n'
            for name,count in bugList.items():
                content = content + name + '：' + str(count) + '\n'
            
            content = content + '\n请及时处理优先级为【高】的缺陷。'
            username = '@all'
            # webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
            utils.msgPush(webhook,topic,content,username)
            time.sleep(5)
            print(topic, content)
        

def test_bugDailyReport_zr():

    headers = utils.get_pingcode_auth('PingCode_zr')
    for project in projectList_zr:
        projectId = project[0]
        webhook = project[1]
        bugList = []
        projectName = None
        activeList = []
        solveList = []
        bugList = []
        activeResults = utils.call('PingCode_zr', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=61b15f63dce97e61deca80e2,61b15f63dce97e61deca80e3,61b15f63dce97e61deca80e5&page_size=100','GET', headers=headers)
        total = utils.json_path(activeResults['body'], '$.total')
        if total[0]:
            page = 1
            activeList = utils.json_path(activeResults['body'], '$.values..assignee.display_name')
            pageTotal = int(total[0])//100
            projectName = utils.json_path(activeResults['body'], '$.values..project.name')[0]
            while(page != 0 and page <= pageTotal):
                activeResults = utils.call('PingCode_zr', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=61b15f63dce97e61deca80e2,61b15f63dce97e61deca80e3,61b15f63dce97e61deca80e5&page_size=100&page_index='+str(page),'GET', headers=headers)
                num = utils.json_path(activeResults['body'], '$.values..assignee.display_name')
                activeList.extend(num)
                page = page+1
        solveResults = utils.call('PingCode_zr', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=61b15f63dce97e61deca80e4,61b15f63dce97e61deca80e8&page_size=100','GET', headers=headers)
        total = utils.json_path(solveResults['body'], '$.total')
        if total[0]:
            page = 1
            if projectName is None:
                projectName = utils.json_path(solveResults['body'], '$.values..project.name')[0]
            solveList = utils.json_path(solveResults['body'], '$.values..created_by.display_name')
            pageTotal = int(total[0])//100
            while(page != 0 and page <= pageTotal):
                solveResults = utils.call('PingCode_zr', '/v1/project/work_items?type=bug&project_ids='+projectId+'&state_ids=61b15f63dce97e61deca80e4,61b15f63dce97e61deca80e8&page_size=100&page_index='+str(page),'GET', headers=headers)
                num = utils.json_path(solveResults['body'], '$.values..created_by.display_name')
                solveList.extend(num)
                page = page+1
        bugList.extend(activeList)
        bugList.extend(solveList)
        if bugList:
            bugList = Counter(bugList)
            topic = '【'+projectName+'】项目当前缺陷情况如下：\n待修复缺陷：'+str(len(activeList))+'\n待验证缺陷：'+str(len(solveList))+'\n'
            content = '\n缺陷分布情况如下：\n'
            for name,count in bugList.items():
                content = content + name + '：' + str(count) + '\n'
            
            content = content + '\n请及时处理优先级为【高】的缺陷。'
            username = '@all'
            #webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
            utils.msgPush(webhook,topic,content,username)
            time.sleep(5)
            print(topic, content)

# 数科院
# 激活：646c55a523823350548b26c1
# 已解决：646c55fa13cb3d13540ec952
# 不解决：646c560fff34a04faacd345c
# 处理中：643614b10a0d63ab9fd81a69
# 重新激活：646c562a6f57855c4326b85c

# 中瑞
# 激活：61b15f63dce97e61deca80e2
# 已解决：61b15f63dce97e61deca80e4
# 不解决：61b15f63dce97e61deca80e8
# 处理中：61b15f63dce97e61deca80e3
# 重新激活：61b15f63dce97e61deca80e5