from utils import utils
from utils.mysqlOp import MysqlOp
import datetime

def notify_cost():

    # 测试
    webhook_test = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
    # 正式 
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e11ce2-c057-431b-8ddd-1f9635ff7bea'
    date = datetime.date.today().strftime("%Y-%m")
    year = int(date.split("-")[0])
    month = int(date.split("-")[1])
    if month == 1:
        year = year - 1
        month = 12
    else:
        month = month - 1

    sql = '''
        select
            OwnerId, OwnerName, TelNumber
        from
            finance_tool.tb_confirminfo
        where
            ConfirmYear = {0}
            and ConfirmMonth = {1}
            and OwnerType = {2}
            and ConfirmState = 0
            and Deleted = 0'''

    result_depart = utils.query_data_mysql('budget_database', sql.format(year, month, 1))
    if result_depart:
        topic = '部门负责人成本填写提醒\n'
        content = '\n请各部门负责人点击下方链接填写上月实际发生成本：'
        telList = []
        for res in result_depart:
            content = content +'\n'+ res[0]+'    '+res[1]
            if res[2]:
                telList.append(res[2])
        content = content + '\n点击填写：https://cwcbfx-finance-ui.lunz.cn/dashboard'
        print(content)
        utils.msgPush(webhook, topic, content, telList)
        utils.msgPush(webhook_test, topic, content, telList)
    else:
        result_bussiness = utils.query_data_mysql('budget_database', sql.format(year, month, 2))
        if result_bussiness:
            topic = '业务线负责人成本确认提醒\n'
            content = '\n请各业务线负责人点击下方链接确认上月实际发生成本：'
            telList = []
            for res in result_bussiness:
                content = content +'\n'+ res[0]+'    '+res[1]
                if res[2]:
                    telList.append(res[2])
            content = content + '\n点击确认：https://cwcbfx-finance-ui.lunz.cn/dashboard'
            print(content)
            utils.msgPush(webhook, topic, content, telList)
            utils.msgPush(webhook_test, topic, content, telList)

def notify_budget():

    # 测试
    webhook_test = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
    # 正式 
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e11ce2-c057-431b-8ddd-1f9635ff7bea'

    date = datetime.date.today().strftime("%Y-%m")
    year = int(date.split("-")[0])

    sql = '''
        select
            OwnerId, OwnerName, TelNumber
        from
            finance_tool.tb_confirminfo
        where
            ConfirmYear = {0}
            and ConfirmMonth = {1}
            and OwnerType = {2}
            and ConfirmState = 0
            and Deleted = 0'''

    result_depart = utils.query_data_mysql('budget_database', sql.format(year, 13, 1))
    result_bussiness = utils.query_data_mysql('budget_database', sql.format(year, 13, 2))
    
    if result_depart or result_bussiness:
        budget = '上半年'
    else:
        result_depart = utils.query_data_mysql('budget_database', sql.format(year, 14, 1))
        result_bussiness = utils.query_data_mysql('budget_database', sql.format(year, 14, 2))
        budget = '下半年'

    if result_depart:
        topic = '部门负责人预算填写提醒\n'
        content = '\n请各部门负责人点击下方链接填写'+budget+'预算：'
        telList = []
        for res in result_depart:
            content = content +'\n'+ res[0]+'    '+res[1]
            if res[2]:
                telList.append(res[2])
        content = content + '\n点击填写：https://cwcbfx-finance-ui.lunz.cn/dashboard'
        print(content)
        utils.msgPush(webhook, topic, content, telList)
        utils.msgPush(webhook_test, topic, content, telList)
    else:
        if result_bussiness:
            topic = '业务线负责人预算确认提醒\n'
            content = '\n请各业务线负责人点击下方链接确认'+budget+'预算：'
            telList = []
            for res in result_bussiness:
                content = content +'\n'+ res[0]+'    '+res[1]
                if res[2]:
                    telList.append(res[2])
            content = content + '\n点击确认：https://cwcbfx-finance-ui.lunz.cn/dashboard'
            print(content)
            utils.msgPush(webhook, topic, content, telList)
            utils.msgPush(webhook_test, topic, content, telList)
