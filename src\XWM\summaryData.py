from utils import utils
from datetime import datetime

def summaryData():
    currentMonth = datetime.now().strftime('%Y-%m-01')
    today = datetime.now().strftime('%Y-%m-%d')
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e7763b9c-c5df-40ec-88ff-02681f315986'
    headers = utils.get_xwm_auth('xwm')
    doctor = utils.call('xwm', '/admin/center/v/overview/doctorview/doctorNum', headers=headers, method='POST')
    organization = utils.call('xwm', '/admin/center/v/overview/orgview/orgNum', headers=headers, method='POST')

    param_yx = {"prodtype": 1}
    param_gj = {"prodtype": 2}
    case_yx = utils.call('xwm', '/admin/center/v/overview/caseview/caseNum', headers=headers, method='POST', param=param_yx)
    case_gj = utils.call('xwm', '/admin/center/v/overview/caseview/caseNum', headers=headers, method='POST', param=param_gj)
    
    agent_param_all = {"page":1,"pagesize":10,"params":"{\"identifier\":\"\",\"name\":\"\",\"email\":\"\",\"payment_days\":\"\",\"contacts\":\"\",\"phone\":\"\",\"create_start\":\"\",\"create_end\":\"\"}"}
    agent_param_month = {"page":1,"pagesize":10,"params":"{\"identifier\":\"\",\"name\":\"\",\"email\":\"\",\"payment_days\":\"\",\"contacts\":\"\",\"phone\":\"\",\"create_start\":\""+currentMonth+"\",\"create_end\":\"\"}"}
    agent_all = utils.call('xwm', '/admin/center/v/client/agent/agentList', headers=headers, method='POST', param=agent_param_all)
    agent_month = utils.call('xwm', '/admin/center/v/client/agent/agentList', headers=headers, method='POST', param=agent_param_month)
    collecting_param_unpaid = {"page":1,"pagesize":10,"type":"0","params":"{}"}
    collecting_param_paid = {"page":1,"pagesize":10,"type":"1","params":"{\"identifier\":\"\",\"name\":\"\",\"doctor\":\"\",\"type\":\"\",\"prodtype\":\"\",\"product_id\":\"\",\"org_id\":\"\",\"salesman\":\"\",\"first_start\":\"\",\"first_end\":\"\",\"agree_start\":\"\",\"agreen_end\":\"\",\"payment\":\"\",\"collect_start\":\"\",\"collect_end\":\"\",\"is_invoice\":\"\",\"invoice_num\":\"\"}"}
    collecting_unpaid = utils.call('xwm', '/admin/center/v/finance/collection/collectingList', headers=headers, method='POST', param=collecting_param_unpaid)
    collecting_paid = utils.call('xwm', '/admin/center/v/finance/collection/collectedList', headers=headers, method='POST', param=collecting_param_paid)

    doctorNum_all = utils.json_path(doctor['body'], '$.data.total.count')
    doctorNum_month = utils.json_path(doctor['body'], '$.data.month.count')
    orgNum_all = utils.json_path(organization['body'], '$.data.total.count')
    orgNum_month = utils.json_path(organization['body'], '$.data.month.count')
    
    caseNum_all_submit_yx = utils.json_path(case_yx['body'], '$.data.submit_case.total.count')
    caseNum_month_submit_yx = utils.json_path(case_yx['body'], '$.data.submit_case.month.count')
    caseNum_all_product_yx = utils.json_path(case_yx['body'], '$.data.product_case.total.count')
    caseNum_month_product_yx = utils.json_path(case_yx['body'], '$.data.product_case.month.count')
    
    caseNum_all_submit_gj = utils.json_path(case_gj['body'], '$.data.submit_case.total.count')
    caseNum_month_submit_gj = utils.json_path(case_gj['body'], '$.data.submit_case.month.count')
    caseNum_all_product_gj = utils.json_path(case_gj['body'], '$.data.product_case.total.count')
    caseNum_month_product_gj = utils.json_path(case_gj['body'], '$.data.product_case.month.count')

    agentNum_all = utils.json_path(agent_all['body'], '$.data.total')
    agentNum_month = utils.json_path(agent_month['body'], '$.data.total')
    collectingNum_unpaid = utils.json_path(collecting_unpaid['body'], '$.data.total')
    collectingNum_paid = utils.json_path(collecting_paid['body'], '$.data.total')

    topic = "<font color=\"warning\">"+today+"</font> 笑唯美业务数据汇总"
    content = f'''
>医生数量：{doctorNum_all[0]}    本月新增：{doctorNum_month[0]}
>机构数量：{orgNum_all[0]}    本月新增：{orgNum_month[0]}
>经销商数：{agentNum_all[0]}        本月新增：{agentNum_month[0]}
>提交病例（隐形）：{caseNum_all_submit_yx[0]}    本月新增：{caseNum_month_submit_yx[0]}
>生产病例（隐形）：{caseNum_all_product_yx[0]}    本月新增：{caseNum_month_product_yx[0]}
>提交病例（硅胶）：{caseNum_all_submit_gj[0]}    本月新增：{caseNum_month_submit_gj[0]}
>生产病例（硅胶）：{caseNum_all_product_gj[0]}    本月新增：{caseNum_month_product_gj[0]}
>待收款：{collectingNum_unpaid[0]}
>已结清：{collectingNum_paid[0]}'''
    utils.msgPush_markdown(webhook=webhook, topic=topic, content=content)