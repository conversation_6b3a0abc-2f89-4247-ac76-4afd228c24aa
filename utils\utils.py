import os
import requests
import time
import json
import pymysql
import yaml
import logging
import redis
from logging.handlers import RotatingFileHandler
from jsonpath import *
from kafka import KafkaConsumer
from urllib.parse import urlencode
from urllib.parse import urlparse
from openpyxl import load_workbook

import zipfile
import datetime
import openpyxl


# 推送企业微信消息
def msgPush(webhook, topic, content, username=None):
    filename = os.path.dirname(os.path.dirname(__file__)) + "/utils/check_info.json"
    f_obj = open(filename, "r", encoding='UTF-8')
    arg = str(json.load(f_obj)).replace("\'", "\"")
    localtime = time.strftime("%Y-%m-%d %H:%M:%S %A", time.localtime())
    if username:
        if type(username) == list:  
            username = '","'.join(username)
    if '"' in content:
        content = content.replace('"', '\'') 
        
    message = arg.replace('date', localtime).replace('topic', str(topic)).replace('warning', str(content)).replace('username', str(username))
    r = requests.post(url=webhook, data=message.encode('utf-8'))

# 推送企业微信markdown消息
def msgPush_markdown(webhook, topic, content):
    filename = os.path.dirname(os.path.dirname(__file__)) + "/utils/check_info_markdown.json"
    f_obj = open(filename, "r", encoding='UTF-8')
    arg = str(json.load(f_obj)).replace("\'", "\"")
    if '"' in content:
        content = content.replace('"', '\"') 
    if '"' in topic:
        topic = topic.replace('"', '\\"') 
        
    message = arg.replace('topic', str(topic)).replace('text', str(content))
    r = requests.post(url=webhook, data=message.encode('utf-8'))

def msgPush_file(webhook_file_id,webhook,file_path):
    """
    <AUTHOR> CJ
    将含文件的消息发送至企业微信

    参数:
    webhook_file_id
    webhook (str): 企业微信的 Webhook 地址
    file_path (str): 文件的绝对路径

    返回:
    requests.Response: 请求的响应
    """
    wx_headers = {'Content-Type':'application/json'}

    if not os.path.isfile(file_path) :
        raise ValueError("提供的路径不是有效的 Excel 文件路径")
  
    data0 = {'file': open(file_path, 'rb')}
    upload_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key='+webhook_file_id+'&type=file'
    response0 = requests.post(url = upload_url, files = data0)
    json_res = response0.json()
    media_id = json_res['media_id']

    data = {
        "msgtype": "file",
        "file": {
            "media_id": media_id
        }
    }
    response = requests.post(webhook, json=data, headers = wx_headers)
    return response


def write_list_to_excel(data_list, headers, file_path, append=False):
    """
    <AUTHOR> CJ
    先创建 Excel 文件，然后将列表格式的数据写入，自动创建多个工作表，
    每个工作表先写入表头，然后写入 X 条数据，顺序执行写入任务，
    写入完成后关闭并保存 Excel 文件

    参数:
    data_list (list): 要写入的数据列表，每个元素为一个列表代表一行数据
    headers (list): 表头列表
    file_path (str): Excel 文件的路径
    append (bool): 是否追加写入，默认为False
    """
    # 如果数据列表为空且文件存在，则删除文件
    if not data_list and os.path.exists(file_path) and not append:
        os.remove(file_path)
        return

    # 如果数据列表为空，则直接返回
    if not data_list:
        return

    if append and os.path.exists(file_path):
        workbook = load_workbook(file_path)
    else:
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)

    num_data = 10000  # 每个 sheet 页写入的数据条数

    if append:
        # 获取最后一个工作表
        sheet = workbook.active
        start_row = sheet.max_row + 1
    else:
        sheet = workbook.create_sheet("Sheet1")
        sheet.append(headers)  # 写入表头
        start_row = 2

    for row_data in data_list:
        if row_data:  # 如果行数据不为空
            sheet.append(row_data)
        else:  # 如果行数据为空
            sheet.append([])  # 写入一个空行

    workbook.save(file_path)
    workbook.close()


def zip_file(file_paths, zip_file_path):
    """
    <AUTHOR> CJ
    将指定的文件路径列表打包成 ZIP 压缩包

    参数:
    file_paths (list): 文件路径列表
    zip_file_path (str): 生成的 ZIP 压缩包的路径
    """
    with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in file_paths:
            zipf.write(file_path, os.path.basename(file_path))


# 连接数据库，执行sql
def sql_excute(dbAlias, sql):
    try:
        db_config = get_config(dbAlias)
        host = db_config["host"]
        user = db_config["user"]
        password = db_config["password"]
        port = db_config["port"]
        database = db_config["database"]
        charset = db_config["charset"]
        connection = pymysql.Connect(
            host=host,
            user=user,
            password=password,
            port=port,
            database=database,
            charset=charset
        )
        cursor = connection.cursor()
        cursor.execute(sql)
        results = cursor.fetchall()
        return results
    except Exception as e:
        print(e)
        topic = 'SQL执行失败\n'
        username = ''
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        content = '\n'+str(e)
        msgPush(webhook=webhook, topic=topic, content=content, username=username)
    finally:
        cursor.close()
        connection.commit()
        connection.close()


# 数据库查询
def select(conn, sql):
    cur = conn.cursor()
    results = None
    try:
        cur.execute(sql)
        results = cur.fetchall()
    except Exception as e:
        print(e)
    return results

# 连接mysql
def mysqlConnect(dbAlias):
        db_config = get_config(dbAlias)
        host = db_config["host"]
        user = db_config["user"]
        password = db_config["password"]
        port = db_config["port"]
        database = db_config["database"]
        charset = db_config["charset"]
        try:
            connection = pymysql.Connect(
                host=host,
                user=user,
                password=password,
                port=port,
                database=database,
                charset=charset
            )
            return connection
        except Exception as e:
            print(e)
            topic = host+' mysql连接失败\n'
            username = ''
            webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
            content = '\n'+str(e)
            msgPush(webhook=webhook, topic=topic, content=content, username=username)

def insert_data_mysql(dbAlias, table, data):
    mysqlConn = mysqlConnect(dbAlias)
    try:
        cursor = mysqlConn.cursor()
        columns = ','.join(data.keys())
        values = ','.join(['%s'] * len(data))
        sql = f"INSERT INTO {table} ({columns}) VALUES ({values})"
        cursor.execute(sql, tuple(data.values()))
        mysqlConn.commit()
    except Exception as e:
        print(e)
        topic = table+' insert操作执行失败\n'
        username = ''
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        content = '\n'+str(e)
        msgPush(webhook=webhook, topic=topic, content=content, username=username)
    finally:
        cursor.close()
        mysqlConn.close()

def query_data_mysql(dbAlias, sql):
    mysqlConn = mysqlConnect(dbAlias)
    try:
        cursor = mysqlConn.cursor()
        cursor.execute(sql)
        results = cursor.fetchall()
        mysqlConn.commit()
        return results
    except Exception as e:
        print(e)
        topic = dbAlias+' select操作执行失败\n'
        username = ''
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        content = '\n'+str(e)
        msgPush(webhook=webhook, topic=topic, content=content, username=username)
    finally:
        cursor.close()
        mysqlConn.close()

# 连接kafka
def kafkaConnect(kafkaAlias):
    try:
        # 从配置文件读取kafka连接需要的参数
        kafka_config = get_config(kafkaAlias)
        topic_name = kafka_config['topic']
        bootstrap_servers = kafka_config['bootstrap_servers']
        group_id = kafka_config['group_id']
        auto_offset = kafka_config['auto_offset_reset']
        protocol_name = kafka_config['security_protocol']
        mechanismn = kafka_config['asl_mechanism']
        plain_username = kafka_config['sasl_plain_username']
        plain_password = kafka_config['sasl_plain_password']
        consumer = KafkaConsumer(topic_name,  # kafka 的topic
                                 bootstrap_servers=bootstrap_servers,  # kafka的地址
                                 group_id=group_id,  # 消费者id
                                 auto_offset_reset=auto_offset,
                                 security_protocol=protocol_name,
                                 sasl_mechanism=mechanismn,
                                 value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                                 sasl_plain_username=plain_username,  # 用户名
                                 sasl_plain_password=plain_password)  # 密码
        return consumer
    except Exception as e:
        print(e)
# 连接redis
def redisConnect(redisAlias):
    redis_config = get_config(redisAlias)
    redis_host = redis_config['host']
    redis_port = redis_config['port']
    redis_password = redis_config['password']
    if 'username' in redis_config:
        redis_password = redis_config['username'] +':'+ redis_password
    redis_db = redis_config['defaultDatabase']
    redis_socket_timeout = redis_config['socket_timeout']
    redis_conn_timeout = redis_config['socket_connect_timeout']
    decode_responses = redis_config['decode_responses']

    try:
        conn = redis.StrictRedis(host=redis_host, port=redis_port, password=redis_password, db=redis_db, socket_timeout=redis_socket_timeout, socket_connect_timeout=redis_conn_timeout, decode_responses=decode_responses)
        return conn
    except Exception as e:
        print(e)
        topic = redis_host+' redis连接失败\n'
        username = ''
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        content = '\n'+str(e)
        msgPush(webhook=webhook, topic=topic, content=content, username=username)


        

# 获取global.yml文件中的配置信息
def get_global(keyword):
    config_path = os.path.dirname(os.path.dirname(__file__)) + '/config/global.yml'
    with open(config_path, 'r', encoding='utf-8') as stream:
        config_all = yaml.load(stream, yaml.FullLoader)
        config_data = config_all[keyword]
        return config_data

# 在global.yml文件中写入配置信息
def set_global(alias, keyword):
    config_path = os.path.dirname(os.path.dirname(__file__)) + '/config/global.yml'
    with open(config_path, 'r', encoding='utf-8') as stream:
        config_all = yaml.load(stream, yaml.FullLoader)
        config_all[alias] = keyword

    with open(config_path, 'w', encoding='utf-8') as stream:
        yaml.dump(config_all, stream)


# 获取config.yml文件中的配置信息
def get_config(keyword):
    environment = get_global('environment')
    config_path = os.path.dirname(os.path.dirname(__file__)) + '/config/config_' + environment + '.yml'
    with open(config_path, 'r', encoding='utf-8') as stream:
        config_all = yaml.load(stream, yaml.FullLoader)
        config_data = config_all[keyword]
        return config_data

# 通过jsonpath表达式从返回值中提取信息
def json_path(data, key):
    if not data:
        return []
    try:
        if isinstance(data, str):
            response_data = json.loads(data)
        else:
            response_data = data
        match_data = jsonpath(response_data, key)
        if match_data == False:
            match_data = []
        return match_data
    except json.JSONDecodeError:
        print(f"JSON解析错误: 输入数据不是有效的JSON格式")
        return []
    except Exception as e:
        print(f"处理JSON数据时出错: {str(e)}")
        return []

# 认证信息获取（认证中心implicit方式）
def get_indentity_password_auth(alias):
    config = get_config(alias)
    indentityConfig = get_config('indentity')
    loginPath = indentityConfig['host'] + indentityConfig['token_path']
    loginData = {
        "client_id": config['client_id'],
        "client_secret": config['client_secret'],
        "grant_type": config['grant_type'],
        "scope": config['scope'],
        "username": config['username'],
        "password": config['password']
    }
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    response = requests.post(url=loginPath, data=loginData, headers=headers)
    tokenType = json_path(response.content, '$.token_type')
    accessToken = json_path(response.content, '$.access_token')
    headers = {"Authorization": tokenType[0] + ' ' + accessToken[0]}
    return headers


# 认证信息获取（认证中心implicit方式）
def get_indentity_implicit_auth(alias, identity = 'indentity'):
    config = get_config(alias)
    indentityConfig = get_config(identity)

    callData = config['callback_param']
    callDath = indentityConfig['callback_path']
    loginArgs = config['login_param']
    loginPath = indentityConfig['host'] + indentityConfig['login_path']
    return_url = callDath + '?' + urlencode(callData)

    up_data = {'ReturnUrl': return_url}
    loginArgs.update(up_data)
    headers = {"content-type": "application/x-www-form-urlencoded;charset=utf-8"}
    response = requests.post(url=loginPath, params=loginArgs, headers=headers)
    res_url = response.url

    token = urlparse(res_url).fragment
    token_list = token.split('&')
    token_response = {}
    for i in range(len(token_list)):
        re_value = token_list[i].split('=')
        key = re_value[0]
        dic_value = re_value[1]
        token_response[key] = dic_value
    authorization = token_response['token_type'] + " " + token_response['access_token']
    headers = {"Authorization": authorization}
    return headers


# 认证信息获取（流通移动端password方式）
def get_ltapp_password_auth(alias):
    config = get_config(alias)
    loginPath = config['host'] + config['login_path']
    loginData = {
        "phoneNumber": config['phoneNumber'],
        "password": config['password'],
        "channelType": config['channelType'],
        "scope": config['scope'],
        "clientId": config['clientId'],
        "clientSecret": config['clientSecret'],
        "accessType": config['accessType']
    }
    loginData = json.dumps(loginData)
    headers = {"content-type": "application/json"}
    response = requests.put(url=loginPath, data=loginData, headers=headers)
    tokenType = json_path(response.content, '$.data.tokenInfo.tokenType')
    accessToken = json_path(response.content, '$.data.tokenInfo.accessToken')
    authorization = tokenType[0] + ' ' + accessToken[0]
    headers = {"Authorization": authorization}
    return headers


# 认证信息获取（PingCode）
def get_pingcode_auth(alias):
    config = get_config(alias)
    loginPath = config['host'] + config['login_path']
    response = requests.get(url=loginPath)
    tokenType = json_path(response.content, '$.token_type')
    accessToken = json_path(response.content, '$.access_token')
    headers = {"Authorization": tokenType[0] + ' ' + accessToken[0]}
    return headers

# 认证信息获取（笑唯美）
def get_xwm_auth(alias):
    config = get_config(alias)
    loginParam = {"username": config['username'],"password":config['password']}
    headers = {"Content-Type": "application/json"}
    response = requests.post(url=config['host'] + config['login_path'], json=loginParam, headers=headers)
    token = json_path(response.content, '$.data.token')
    appsid = json_path(response.content, '$.data.appsid')
    headers["token"] = token[0]
    headers["appsid"] = appsid[0]
    return headers


# 根据账号和密码从认证中心获取认证信息
def get_auth_info():
    config_auth = get_config('Auth_info')
    url = config_auth['host']
    username = config_auth['username']
    password = config_auth['password']
    Authorization_identify = config_auth['Authorization']
    header = {'Content-Type': 'application/x-www-form-urlencoded', 'Authorization': Authorization_identify}
    form_data = {'username': username, 'password': password, 'grant_type': 'password'}
    # print("信息头:" + str(header))
    # print("请求数据为：" + str(form_data))
    # print("url:" + url)
    reponse = requests.post(url, data=form_data, headers=header)
    # 拼接认证信息
    Authorization = 'Bearer ' + reponse.json()['access_token']
    return Authorization

    # 调用接口

def call(alias, path, method, headers={}, param=None, type=None):
    """
    request:
    alias：业务系统系统别名，用于获取配置信息
    path：接口地址，以'/'开头
    method：接口请求方法
    headers：包含认证信息的请求头
    param：接口传参，不传默认为空
    type：参数格式，不传默认为json格式，如需form格式，在调用时指定type=from
    """
    method = method.upper()
    if alias:
        config = get_config(alias)
        path = config['host'] + path
    if type == 'form':
        headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8'
    else:
        headers['Content-Type'] = 'application/json;charset=utf-8'
    if method == 'GET':
        response = requests.get(url=path, headers=headers)
    elif method == 'POST' and type is None:
        response = requests.post(url=path, json=param, headers=headers)
    elif method == 'POST' and type is not None:
        response = requests.post(url=path, data=param, headers=headers)
    elif method == 'PUT' and type is None:
        response = requests.put(url=path, json=param, headers=headers)
    elif method == 'PUT' and type is not None:
        response = requests.put(url=path, data=param, headers=headers)
    elif method == 'DELETE' and type is None:
        response = requests.delete(url=path)
    elif method == 'OPTIONS':
        response = requests.options(url=path, headers=headers)
    else:
        print('参数不合法！')

    result = {}
    result['body'] = response.content.decode('utf-8')
    result['code'] = response.status_code
    return result


# 添加读取json文件的方法
def get_json_info(file_path):
    dir_path = os.path.dirname(os.path.dirname(__file__))
    with open(dir_path + file_path, 'r', encoding='utf-8') as file:
        text = file.read()
        json_data = json.loads(text)
    return json_data

def log(log_path):
    logger = logging.getLogger('my_logger')
    logger.setLevel(logging.INFO)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_path, encoding='utf-8')

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # 将文件处理器添加到日志记录器
    logger.addHandler(file_handler)
    return logger
