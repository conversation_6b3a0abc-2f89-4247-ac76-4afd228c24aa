from utils import utils

def operate_info():

    sql_user_num = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 134'''
    sql_user_yesterday_increase = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 134
            and b.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
            and b.CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
            '''
    sql_user_month_increase = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 134
            and b.CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
            and b.CreatedAt<now()'''
    sql_user_yesterday_active = '''
        select 
            count(1)
        from
            (
            select
                distinct UserId
            from
                zcp_commentcenter.cm_postinfo
            where
                CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
                    and CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
                        and PostType in (1, 2, 3, 4, 5)
                            and ChannelType = 134
                    union
                        select
                            distinct a.UserId
                        from
                            zcp_commentcenter.cm_post_comment a
                            -- 一级评论
                        inner join zcp_commentcenter.cm_postinfo b 
        on
                            a.PostId = b.id
                            and b.Deleted = 0
                        where
                            a.Deleted = 0
                            and b.ChannelType = 134
                            and a.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
                                and a.CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
                        union
                            select
                                distinct a.UserId
                            from
                                zcp_commentcenter.cm_post_commentdetail a
                                -- 二级评论
                            inner join zcp_commentcenter.cm_postinfo b 
        on
                                a.PostId = b.id
                                and b.Deleted = 0
                            where
                                a.Deleted = 0
                                and b.ChannelType = 134
                                and a.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
                                    and a.CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
                            union
                                select
                                    distinct a.UserId
                                from
                                    zcp_commentcenter.rel_user_post a
                                    -- 点赞，收藏
                                inner join zcp_commentcenter.cm_postinfo b 
        on
                                    a.PostId = b.id
                                    and b.Deleted = 0
                                where
                                    a.Deleted = 0
                                    and b.ChannelType = 134
                                    and a.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
                                        and a.CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
                                            and a.RelationType in (1, 2)
                                    union
                                        select
                                            distinct UserId
                                        from
                                            zcp_commentcenter.rel_user -- 关注
                                        where
                                            CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
                                                and CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
                                                    and ChannelType = 134) aa'''
    sql_user_month_active = '''
        select
	        count(1)
        from
            (
            select
                distinct UserId
            from
                zcp_commentcenter.cm_postinfo
            where
                CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
                    and CreatedAt<now()
                    and PostType in (1, 2, 3, 4, 5)
                        and ChannelType = 134
                union
                    select
                        distinct a.UserId
                    from
                        zcp_commentcenter.cm_post_comment a
                        -- 一级评论
                    inner join zcp_commentcenter.cm_postinfo b 
        on
                        a.PostId = b.id
                        and b.Deleted = 0
                    where
                        a.Deleted = 0
                        and b.ChannelType = 134
                        and a.CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
                            and a.CreatedAt<now()
                    union
                        select
                            distinct a.UserId
                        from
                            zcp_commentcenter.cm_post_commentdetail a
                            -- 二级评论
                        inner join zcp_commentcenter.cm_postinfo b 
        on
                            a.PostId = b.id
                            and b.Deleted = 0
                        where
                            a.Deleted = 0
                            and b.ChannelType = 134
                            and a.CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
                                and a.CreatedAt<now()
                        union
                            select
                                distinct a.UserId
                            from
                                zcp_commentcenter.rel_user_post a
                                -- 点赞，收藏
                            inner join zcp_commentcenter.cm_postinfo b 
        on
                                a.PostId = b.id
                                and b.Deleted = 0
                            where
                                a.Deleted = 0
                                and b.ChannelType = 134
                                and a.CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
                                    and a.CreatedAt<now()
                                    and a.RelationType in (1, 2)
                            union
                                select
                                    distinct UserId
                                from
                                    zcp_commentcenter.rel_user -- 关注
                                where
                                    CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
                                        and CreatedAt<now()
                                        and ChannelType = 134) aa'''
    sql_works_num = '''
        select 
            count(1)
        from
            zcp_commentcenter.cm_postinfo
        where
            PostType in (1, 2, 3, 4, 5)
            and ChannelType = 134
            and CheckType = 2'''
    sql_works_yesterday_increase = '''
        select 
            count(1)
        from
            zcp_commentcenter.cm_postinfo
        where
            PostType in (1, 2, 3, 4, 5)
            and ChannelType = 134
            and CheckType = 2
            and CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
            and CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')'''
    sql_works_month_increase = '''
        select
            count(1)
        from
            zcp_commentcenter.cm_postinfo
        where
            PostType in (1, 2, 3, 4, 5)
            and ChannelType = 134
            and CheckType = 2
            and CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
            and CreatedAt<now()	'''

    webhook = utils.get_config('bananaRC_data')['webhook']
    dbAlias = 'lt_database'
    user_num = utils.query_data_mysql(dbAlias, sql_user_num)
    user_yesterday_increase = utils.query_data_mysql(dbAlias, sql_user_yesterday_increase)
    user_month_increase = utils.query_data_mysql(dbAlias, sql_user_month_increase)
    user_yesterday_active = utils.query_data_mysql(dbAlias, sql_user_yesterday_active)
    user_month_active = utils.query_data_mysql(dbAlias, sql_user_month_active)
    works_num = utils.query_data_mysql(dbAlias, sql_works_num)
    works_yesterday_increase = utils.query_data_mysql(dbAlias, sql_works_yesterday_increase)
    works_month_increase = utils.query_data_mysql(dbAlias, sql_works_month_increase)

    topic = '<font color=\"info\">模友</font> 监控数据统计'
    content = f'''
        >用户总数：{user_num[0][0]}
        >昨日新增：{user_yesterday_increase[0][0]}
        >当月新增：{user_month_increase[0][0]}
        >昨日活跃：{user_yesterday_active[0][0]}
        >当月活跃：{user_month_active[0][0]}
        >作品总数：{works_num[0][0]}
        >昨日新增：{works_yesterday_increase[0][0]}
        >当月新增：{works_month_increase[0][0]}'''
    utils.msgPush_markdown(webhook=webhook, topic=topic, content=content)









