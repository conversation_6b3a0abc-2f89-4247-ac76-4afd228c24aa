# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
import datetime
from utils.mysqlOp import MysqlOp
from utils import utils

# 状态为[部分坏账]的应收单坏账和回款金额数据核对
def check_partialBadPrice():
    # 实例数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    today = datetime.date.today()
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    yesterday = yesterday.strftime("%Y-%m-%d")
    # 正式环境用
    sql = '''
          select distinct a.`OrderId` ,b.`OrderNo`, b.`OrderType`,ifnull(ifnull(b.`OrderAdjustPrice`, b.`OrderRetrialPrice`), b.`OrderPrice`) as OrderPrice from `finance_main`.`main_badpriceorderinfo`  a, `finance_basic`.`basic_orderinfo` b , `finance_main`.`main_badpriceinfo` c
          where a.`OrderId` =b.`OrderId`
          and a.`BadPriceId` =c.`Id`
          and a.`Deleted` =0
          and b.`Deleted` =0
          and c.`Deleted` =0
          and c.`AuditTime`<'{0}'
          and c.`AuditTime`>='{1}'
          and b.`ReceiptState` =4
          and c.BadPriceStatus =4
          '''.format(today, yesterday)
    # 测试环境用
    # sql = '''
    #       select distinct a.`OrderId` ,b.`OrderNo`, b.`OrderType`,ifnull(ifnull(b.`OrderAdjustPrice`, b.`OrderRetrialPrice`), b.`OrderPrice`) as OrderPrice from `finance_main`.`main_badpriceorderinfo`  a, `finance_basic`.`basic_orderinfo` b , `finance_main`.`main_badpriceinfo` c
    #       where a.`OrderId` =b.`OrderId`
    #       and a.`BadPriceId` =c.`Id`
    #       and a.`Deleted` =0
    #       and b.`Deleted` =0
    #       and c.`Deleted` =0
    #       and c.`AuditTime`<'2023-12-21'
    #       and c.`AuditTime`>='2023-05-01'
    #       and b.`ReceiptState` =4
    #       and c.BadPriceStatus =4
    #       '''
    # print(sql)
    result = utils.select(conn, sql)
    orderInfos_list = []
    orderInfo_dic = {}
    content = '\n'
    robotId = utils.get_config('gfs_report')['robotId']
    username = '18661922397'
    topic = '【部分坏账】的应收单异常数据如下，请核实:'
    if result:
        # print('查询出的订单' + str(result))
        for row in result:
            # print('每行查询出的结果' + str(row))
            orderInfo_dic['orderId'] = row[0]
            orderInfo_dic['orderNo'] = row[1]
            orderInfo_dic['orderType'] = row[2]
            orderInfo_dic['orderPrice'] = row[3]
            orderInfos_list.append(orderInfo_dic)
            orderInfo_dic = {}
    # print(orderInfos_list)
    for i in range(len(orderInfos_list)):
        materialTotalPrice = {}
        tempPrice = {}
        tempPrice1 = {}
        goodsTotalPrice = {}
        badMainPartPrice = []
        receiptMainPartPrice = []
        mbadMainPartPrice = []
        mreceiptMainPartPrice = []
        sumBadPrice = 0
        sumReceiptPrice = 0
        orderId = orderInfos_list[i].get('orderId')
        orderNo = orderInfos_list[i].get('orderNo')
        # 判断订单三层表中物料层的坏账和回款金额求和 与 商品层的坏账和回款金额求和是否相等
        sql_material = '''select sum( IFNULL(`MaterialReceiptPrice`,0)) AS TotalMatarialReceiptPrice, sum( IFNULL(`MaterialBadPrice`,0)) AS TotalMatarialBadPrice, `OrderItemId`  from `finance_basic`.`basic_orderitemdetail` 
        where `OrderItemId` in 
        (select `OrderItemId` from `finance_basic`.basic_orderitem where `OrderId` ='{0}') GROUP BY `OrderItemId`
        '''.format(orderId)
        sql_goods = '''select  IFNULL(`GoodsReceiptPrice`,0)  AS TotalGoodsReceiptPrice, IFNULL(`GoodsBadPrice`,0) AS TotalGoodsBadPrice , `OrderItemId`  from `finance_basic`.basic_orderitem 
        where `OrderId` ='{0}' 
        '''.format(orderId)
        sql_goodsTotal = '''SELECT sum(IFNULL( a.`GoodsReceiptPrice`, 0 )*IF( b.`OrderType` IN ( 3, 4, 9, 10, 12 ),- 1, 1 )+ IFNULL( a.GoodsBadPrice, 0 )) AS totalPrice 
        FROM`finance_basic`.basic_orderitem a,`finance_basic`.`basic_orderinfo` b 
        WHERE a.`OrderId` = b.`OrderId` AND a.`Deleted` = 0 AND b.`Deleted` = 0 AND a.`OrderId` = '{0}'
        '''.format(orderId)
        sql_badPrice = '''SELECT sum( a.OrderBadPrice ) AS OrderBadPrice 
        FROM `finance_main`.`main_badpriceorderinfo` a, finance_main.main_badpriceinfo b 
        WHERE a.BadPriceId = b.id
        and b.BadPriceStatus =4 and a.`OrderId` = '{0}'  AND a.`Deleted` =0
        '''.format(orderId)
        # 负数单展示为负数
        sql_receiptPrice = '''select sum(a.OrderReceiptPrice) as totalOrderReceiptPrice from `finance_main`.`main_receiptorderinfo`a ,finance_main.main_receiptinfo c 
        where a.ReceiptId = c.Id 
        and a.`OrderId` ='{0}' 
        and a.`Deleted` = 0 
        and c.Deleted =0 
        and c.RelateStatus in (1,2)
        '''.format(orderId)
        sql_badMainPartPrice = '''select a.mainpartId,a.mainpartName,sum(a.orderBadPrice) as orderBadPrice from `finance_main`.`main_badpriceordermainpartinfo` a,finance_main.main_badpriceinfo b
         where a.BadPriceId = b.id 
         and a.`OrderId`  ='{0}' 
         and a.deleted =0
         and b.BadPriceStatus =4
         GROUP BY a.MainPartId
        '''.format(orderId)
        # 负数单展示为负数
        sql_receiptMainPartPrice = '''select a.mainpartId, sum(a.orderReceiptPrice) as orderReceiptPrice from `finance_main`.`main_receiptordermainpartinfo` a, finance_main.main_receiptinfo c
         where a.ReceiptId = c.id
         and a.`OrderId` ='{0}' 
         and a.`Deleted` =0
         and c.RelateStatus in (1,2)
         GROUP BY a.MainPartId
        '''.format(orderId)
        sql_materialMainPartPrice = '''select mainpartId,mainpartName,sum(IFNULL(materialBadPrice,0)) as mainpartTotalBadPrice,
        sum(IFNULL(materialReceiptPrice,0)*IF (a.`OrderType` in (3,4,9,10,12),-1,1)) as mainpartTotalReceiptPrice from finance_basic.basic_orderinfo a,finance_basic.basic_orderitem b,finance_basic.basic_orderitemDetail c
        where a.orderId = b.orderId 
        and b.orderitemId = c.orderitemId
        and a.deleted =0
        and b.deleted =0
        and c.deleted =0
        and a.orderId = '{0}'
        GROUP BY mainpartId
        '''.format(orderId)
        # print(sql_goodsTotal)
        result_material = utils.select(conn, sql_material)
        result_goods = utils.select(conn, sql_goods)
        result_goodsTotal = utils.select(conn, sql_goodsTotal)
        result_badPrice = utils.select(conn, sql_badPrice)
        result_receiptPrice = utils.select(conn, sql_receiptPrice)
        result_badMainPartPrice = utils.select(conn, sql_badMainPartPrice)
        result_receiptMainPartPrice = utils.select(conn, sql_receiptMainPartPrice)
        result_materialMainPartPrice = utils.select(conn, sql_materialMainPartPrice)

        # print(result_goodsTotal)
        if result_material:
            for row in result_material:
                tempPrice['TotalReceiptPrice'] = row[0]
                tempPrice['TotalBadPrice'] = row[1]
                materialTotalPrice[row[2]] = tempPrice
                tempPrice = {}
        if result_goods:
            for row in result_goods:
                tempPrice['TotalReceiptPrice'] = row[0]
                tempPrice['TotalBadPrice'] = row[1]
                goodsTotalPrice[row[2]] = tempPrice
                tempPrice = {}
        # print(materialTotalPrice)
        # print(goodsTotalPrice)
        for key1 in materialTotalPrice:
            for key2 in materialTotalPrice[key1]:
                if materialTotalPrice[key1][key2] != goodsTotalPrice.get(key1).get(key2):
                    msg = "订单{}中的商品ID{}下的物料层的坏账和回款金额汇总和商品层的坏账和回款金额不相等".format(orderNo, key1)
                    content = content + msg + '\n'
        # 判断订单三层表中商品层的坏账和回款金额求和 与 订单金额是否相等
        if result_goodsTotal:
            # print(result_goodsTotal[0][0])
            # print(orderInfos_list[i].get('orderPrice'))
            if result_goodsTotal[0][0] != orderInfos_list[i].get('orderPrice'):
                msg = "订单{}商品层的坏账和回款金额汇总与订单金额不相等".format(orderNo)
                content = content + msg + '\n'
        # 判断坏账主表的坏账金额和回款主表的回款金额求和 与 订单金额是否相等
        if result_badPrice and result_receiptPrice:
            if result_badPrice[0][0] + result_receiptPrice[0][0] != orderInfos_list[i].get('orderPrice'):
                msg = "订单{}坏账主表的坏账金额和回款主表的回款金额汇总与订单金额不相等".format(orderNo)
                content = content + msg + '\n'
        # 判断各个业务主体的坏账主表的坏账金额和回款主表的回款金额求和 与 订单物料层的金额是否相等
        # print(result_materialMainPartPrice)
        if result_badMainPartPrice:
            for row in result_badMainPartPrice:
                tempPrice['mainPartId'] = row[0]
                tempPrice['mainPartBadPrice'] = row[2]
                badMainPartPrice.append(tempPrice)
                tempPrice = {}
        if result_receiptMainPartPrice:
            for row in result_receiptMainPartPrice:
                tempPrice['mainPartId'] = row[0]
                tempPrice['mainPartReceiptPrice'] = row[1]
                receiptMainPartPrice.append(tempPrice)
                tempPrice = {}
        if result_materialMainPartPrice:
            for row in result_materialMainPartPrice:
                tempPrice['mainPartId'] = row[0]
                tempPrice['mainPartBadPrice'] = row[2]
                mbadMainPartPrice.append(tempPrice)
                tempPrice = {}
                tempPrice1['mainPartId'] = row[0]
                tempPrice1['mainPartReceiptPrice'] = row[3]
                mreceiptMainPartPrice.append(tempPrice1)
                tempPrice1 = {}
        for x in range(len(badMainPartPrice)):
            sumBadPrice += badMainPartPrice[x].get('mainPartBadPrice')
            if badMainPartPrice[x] not in mbadMainPartPrice:
                msg = "订单{}中的主体{}坏账主体表的坏账金额与物料表中的该主体坏账金额汇总不相等".format(orderNo,
                                                                      badMainPartPrice[x].get('mainPartId'))
                content = content + msg + '\n'

        for y in range(len(receiptMainPartPrice)):
            sumReceiptPrice += receiptMainPartPrice[y].get('mainPartReceiptPrice')
            if receiptMainPartPrice[y] not in mreceiptMainPartPrice:
                msg = "订单{}中的主体{}回款主体表的回款金额与物料表中的该主体回款金额汇总不相等".format(orderNo,
                                                                      receiptMainPartPrice[y].get('mainPartId'))
                content = content + msg + '\n'

        if (sumBadPrice + sumReceiptPrice) != orderInfos_list[i].get('orderPrice'):
            msg = "订单{}坏账主体表的坏账金额和回款主体表的回款金额汇总与订单金额不相等".format(orderNo)
            content = content + msg + '\n' + '--------------' + '\n'
    if content != '\n':
        utils.msgPush(robotId, topic, content, username)
    conn.close()


if __name__ == '__main__':
    check_partialBadPrice()
