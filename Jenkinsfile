pipeline {
    agent { label 'linux' }

    // setting default value to using cache
    parameters { booleanParam(name: 'useCache', defaultValue: true, description: '') }

    // keep 30 days, 200 builds.
    options {
        buildDiscarder(logRotator(artifactDaysToKeepStr: '',
        artifactNumToKeepStr: '', daysToKeepStr: '30', numToKeepStr: '200'))
        timestamps()
    }

    // global environment
    environment {
        NAMESPACE = 'lunz-zcp'
        IMAGE_NAME = 'qa-autocheck'
        IMAGE_TAG = 'snapshot'
        HW_REGISTRY = 'swr.cn-east-3.myhuaweicloud.com'
        HW_REGISTRY_CREDENTIAL_ID = 'huaweiregistry'
        HW_IMAGE_URL= "${HW_REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
        DOCKERFILE = 'Dockerfile'
        BRANCH_PREFIX = 'master'
        DETAILS_BUILDING = false
        // DEPLOY_BUILDING = true
        WECHAT_CREDENTIAL_ID = credentials('qywxRabotUrl')
        WECHAT_USER_ID = """${sh(
                returnStdout: true,
                script: 'git show -s --pretty=%an'
            )}"""
    }

    stages {
        
        stage ('Judge'){
            steps{
                script{
                    if (env.BRANCH_NAME == BRANCH_PREFIX) {
                        DETAILS_BUILDING =true
                        echo "${HW_IMAGE_URL}"
                    }
                }
            }
        }

        stage('Build'){

            when {
                expression {
                    DETAILS_BUILDING
                }
            }

            stages{
                stage ('Push'){
                    steps{
                        script{
                            def myImage = null                            
                            docker.withRegistry("https://${HW_REGISTRY}", "${HW_REGISTRY_CREDENTIAL_ID}") {
                                myImage = docker.build("${HW_IMAGE_URL}", " -f ${DOCKERFILE} .")      
                                myImage.push()
                            }
                        }
                    }
                }
            }
        }
    }

     post {

        always {
            sh """
                curl '${WECHAT_CREDENTIAL_ID}' \
                -H 'Content-Type: application/json' \
                -d '
                {
                    "msgtype": "text",
                    "text": {
                        "content": "Jenkins构建通知: \\n项目名称: ${env.JOB_NAME} \\n构建结果: ${currentBuild.result} \\n构建时长: ${currentBuild.duration}ms \\n构建日志地址: ${env.BUILD_URL}console",
                        "mentioned_list":["${WECHAT_USER_ID.split('-')[0].trim()}"]
                    }
                }'
                """
        }
    }
}