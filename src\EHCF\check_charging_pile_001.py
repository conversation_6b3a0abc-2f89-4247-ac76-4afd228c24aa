import datetime
import pymysql
from utils import utils

'''EHCF-充电桩
当天的开始、截至日期如下，执行周期半小时执行一次：
开始日期=当期日期 0点0分0秒   截至日期=当期日期 10点0分0秒
开始日期=当期日期 10点0分0秒  截至日期=当期日期 12点0分0秒
开始日期=当期日期 12点0分0秒  截至日期=当期日期 15点0分0秒
开始日期=当期日期 15点0分0秒  截至日期=当期日期 18点0分0秒
开始日期=当期日期 18点0分0秒  截至日期=当期日期 23点59分59秒'''

webhook ='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f9730b84-7a37-400d-a42b-ddcb2a65917a'
username = '13061378580' #重点@某人，为空@全员


def check_EHCF_charging_pile_setup(): 
    Authorization = utils.get_indentity_password_auth('EHCF')
    header={'Ehcforigintag':'1'}

    global headers
    headers={**Authorization,**header} #获取组合后的headers信息

    global today_time
    today_time=str(datetime.date.today()) #当天

def check_EHCF_charging_pile_001(): 

    today_time_1=today_time+' '+'00:00:00'
    today_time_2=today_time+' '+'10:00:00' #开始日期=当期日期 0点0分0秒  截至日期=当期日期 10点0分0秒

    param = {'syncAll': 'true','serviceProviderCode':'1067','startDate':today_time_1,'endDate':today_time_2}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body1
    result_body1=result['body']
    assert "数据" in result_body1,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_charging_pile_002(): 

    today_time_3=today_time+' '+'10:00:00'
    today_time_4=today_time+' '+'12:00:00' #开始日期=当期日期 10点0分0秒  截至日期=当期日期 12点0分0秒
    param = {'syncAll': 'true','serviceProviderCode':'1067','startDate':today_time_3,'endDate':today_time_4}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body2
    result_body2=result['body']
    assert "数据" in result_body2,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_charging_pile_003(): 

    today_time_5=today_time+' '+'12:00:00'
    today_time_6=today_time+' '+'15:00:00' #开始日期=当期日期 12点0分0秒  截至日期=当期日期 15点0分0秒
    param = {'syncAll': 'true','serviceProviderCode':'1067','startDate':today_time_5,'endDate':today_time_6}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body3
    result_body3=result['body']
    assert "数据" in result_body3,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_charging_pile_004(): 

    today_time_7=today_time+' '+'15:00:00'
    today_time_8=today_time+' '+'18:00:00' #开始日期=当期日期 15点0分0秒  截至日期=当期日期 18点0分0秒
    param = {'syncAll': 'true','serviceProviderCode':'1067','startDate':today_time_7,'endDate':today_time_8}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body4
    result_body4=result['body']
    assert "数据" in result_body4,"执行失败" #断言结果存在“数据”信息，否则执行失败

def check_EHCF_charging_pile_005(): 

    today_time_9=today_time+' '+'18:00:00'
    today_time_10=today_time+' '+'23:59:59' #开始日期=当期日期 18点0分0秒  截至日期=当期日期 23点59分59秒
    param = {'syncAll': 'true','serviceProviderCode':'1067','startDate':today_time_9,'endDate':today_time_10}
    result = utils.call('EHCF','/api/DataSync/DataSyncCheck', 'GET', headers=headers,param=param)
    print(result['body'])

    global result_body5
    result_body5=result['body']
    assert "数据" in result_body5,"执行失败" #断言结果存在“数据”信息，否则执行失败 

def check_EHCF_msg(): #执行失败，发送企业微信消息
    result_body=[]
    result_body.append(result_body1)
    result_body.append(result_body2)
    result_body.append(result_body3)
    result_body.append(result_body4)
    result_body.append(result_body5)

    if result_body:
        # topic=("<font color=blue>EHCF-充电桩-定时任务-当天</font>\n" +
        #        "<font color=red>执行失败</font>\n") #使用markdown设置字体颜色，需修改check_info.json文件
        
        topic=("EHCF-充电桩-定时任务-当天-执行失败") 
        content = ''
        arr = []
        for record in result_body:
            if "数据" not in record :
                arr.append(record)
                content = content + '\n' + str(record)
        if arr:
            utils.msgPush(webhook,topic,content,username)



