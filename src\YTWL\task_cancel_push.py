import datetime
from utils import utils
today = datetime.date.today().strftime("%Y-%m-%d")
yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69d53887-3d1e-486f-8a71-35ca4ab35445'
username = ''

# 任务取消推送
#def task_cancel_push():
# task_cancel_push    param = {"filter":{}}
#     webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69d53887-3d1e-486f-8a71-35ca4ab35445'
#     result = utils.call('InsuranceService', '/api/v1/field/task', 'DELETE')
#     code = result['code']
#     if code == 204:
#         sql = 'SELECT task_name FROM ytwlcenter_fat.tb_taskinfo where id = '
#         result = utils.query_data_mysql('task_name', sql)
#         if result:
#             topic = '今日有以下任务被取消，请确认：'
#             content = ''
#             arr = []
#             for record in result:
#                     arr.append(record)
#                     content = content + '\n'
#         username = '18153212717'
#         utils.msgPush(webhook, topic, content, username)
#
#     else:
#         topic = '接口调用失败\n'
#         content = '\n/api/v1/field/task接口调用失败！\n'
#         username = '18153212717'
#         utils.msgPush(webhook, topic, content, username)

def task_cancel_push():
        #今日新增任务数量
        sql_todaystartCount = "select count(1) from ytwlcenter_fat.tb_taskinfo where to_days(created_at) =to_days(now())"
        todaystartCount = utils.query_data_mysql('ytwl_database', sql_todaystartCount)
        if todaystartCount[0][0] >0:
            topic_taskstart = '今日新增任务数量\n'
            taskstart = '\n新增任务：' + str(todaystartCount[0][0])
            utils.msgPush(webhook, topic_taskstart, taskstart, username)

        sql = 'select (select base_name from ytwlcenter_fat.tb_baseinfo where id =task.base_id), task_name,operator from ytwlcenter_fat.tb_taskinfo task where release_status =3 AND to_days(updated_at) =to_days(now());'
        result = utils.query_data_mysql('ytwl_database', sql)
        if result:
            topic = '今日有以下任务被取消，请核对：\n'
            content = ''
            arr = []
            for record in result:
                arr.append(record)
                content = content + '\n' + '所属基地：'+' '+str(record[0])+ '  ' +'任务名称：'+' '+str(record[1]) + '  ' '执行人：' + str(record[2])
            utils.msgPush(webhook, topic, content, username)

