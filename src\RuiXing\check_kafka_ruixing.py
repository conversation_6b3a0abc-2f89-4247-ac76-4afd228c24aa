from kafka import KafkaConsumer
import json
import datetime
from utils import utils
import pymysql

def start():
    try:
        # 从配置文件读取custInfo相关的参数,以数组的形式返回
        custInfo_config = utils.get_config('rx_custInfo')
        # 定义一个dict
        msg_dict = {}
        arr =[]
        consumer = utils.kafkaConnect('rx_kafka')
        for message in consumer:
            msg = message.value
            print(msg)
            if 'plateNumber' in msg.keys():
                plateNumber = msg["plateNumber"]
                deviceNumber = msg["deviceNumber"]
                alarmType = msg["alarmType"]
                alarmName = msg["alarmName"]
                timeStamp = msg['t']
                # 将时间戳转化为日期格式
                time_kafka = datetime.datetime.fromtimestamp(timeStamp / 1000)
                startTime = time_kafka.strftime("%Y-%m-%d %H:%M:%S")
                dict_key = deviceNumber + '_' + alarmType
                username = ''
                robotId = custInfo_config['robotId']
                custId = custInfo_config['custId']
                alarmType_str = custInfo_config["alarmType"]
                alarmType_list = str(alarmType_str).split(',')
                sql = 'select PlateNumber from tb_carinfo tc WHERE  OrganId IN (select id from tb_organinfo to2 WHERE INSTR(code,\''+custId+'\')>0)'
                if plateNumber not in arr:
                    arr =[]
                    result = utils.query_data_mysql('rx_database', sql)
                    for row in result:
                        arr.append(row[0])
                if plateNumber in arr:
                    if alarmType in alarmType_list:
                        # 往字典中写入第一条数据
                        if dict_key not in msg_dict.keys():
                            msg_dict[dict_key] = timeStamp
                            # 推送消息
                            content = '车辆【' + plateNumber + '】设备号【' + deviceNumber + '】，在【' + startTime + '】发生了【' + alarmName + '】，请注意处理。'
                            print('第一次推送预警' + content)
                            utils.msgPush(robotId, content, username)
                        # 取出字典中对应的时间戳
                        time_dic = datetime.datetime.fromtimestamp(msg_dict[dict_key] / 1000)
                        # 计算同一设备同一报警类型下 消息队列与字典中的时间差(分钟)
                        minutes = (time_kafka - time_dic).total_seconds() // 60
                        if minutes >= 10:
                            # 更新时间戳
                            msg_dict[dict_key] = timeStamp
                            # 推送消息
                            content = '车辆【' + plateNumber + '】设备号【' + deviceNumber + '】，在【' + startTime + '】持续产生【' + alarmName + '】，请注意处理。'
                            print('非第一次推送预警' + content)
                            utils.msgPush(robotId, content, username)
    except Exception as e:
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        errmsg = str(msg)+'\n错误信息：'+str(e)
        utils.msgPush(webhook, errmsg, '')