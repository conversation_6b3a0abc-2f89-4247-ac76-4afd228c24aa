from utils import utils
import datetime
import uuid

def redisToDb():

    redisConn = utils.redisConnect('gateway_redis')
    today = datetime.datetime.now()
    yesterday = today - datetime.timedelta(days=1)
    yesterdayStr = yesterday.strftime("%Y%m%d")
    # key通过|分割后生成的列表
    splitList = []
    # sql中的data
    rowdict = {}
    #  异常数据列表
    errorList = []

    # 通配符查询匹配的key
    pattern = yesterdayStr+'|*'
    keys = redisConn.keys(pattern)
    print(keys)
    print(pattern)
    for key in keys:
        # 查询key对应的value
        value = redisConn.get(key)
        splitList = key.split('|')
        if len(splitList) == 4:
            rowdict['Id'] = uuid.uuid4()
            rowdict['Date'] = yesterday
            rowdict['ApiName'] = splitList[1]
            rowdict['RequestNum'] = value
            rowdict['Customer'] = splitList[2]
            rowdict['StatusCode'] = splitList[3]
            rowdict['insertAt'] = today
            rowdict['Deleted'] = 0
            print(rowdict)
            utils.insert_data_mysql(dbAlias='apiplat_database', table='api_status_statistics', data=rowdict)
            
        else:
            errorList.append(key)
            topic = 'gateway-redis中存在异常数据\n'
            username = ''
            webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
            for item in errorList:
                content = content+item+'\n'
    if errorList:
        utils.msgPush(webhook=webhook, topic=topic, content=content, username=username)

        