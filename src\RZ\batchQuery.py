from utils import utils
from openpyxl import load_workbook
import concurrent.futures
from threading import Lock
from datetime import datetime

def process_single_record(vin, date, header):
    try:
        # 检查日期格式并转换
        if isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d')
        date_str = date.strftime('%Y-%m-%d')
        url = '/api/v1/insurance/getInsuranceScore?date='+date_str+'&vinNumber='+vin
        result = utils.call('rz_external', url, 'GET', headers=header)
        isSuccess = utils.json_path(result['body'], '$.isSuccess')[0]
        if isSuccess == '1':
            frequencyPrediction = utils.json_path(result['body'], '$.data.frequencyPrediction')[0]
            frequencyScore = utils.json_path(result['body'], '$.data.frequencyScore')[0]
            frequencyModelVersion = utils.json_path(result['body'], '$.data.frequencyModelVersion')[0]
            duration = utils.json_path(result['body'], '$.data.duration')[0]
            item = [vin, date_str, frequencyPrediction, frequencyScore,frequencyModelVersion,duration,result['body']]
        else:
            item = [vin, date_str, '','','','',result['body']]
        print(url, item)
        return item
    except Exception as e:
        print(f"处理记录时出错: {str(e)}, vin: {vin}, date: {date}")
        return None

def batchQuery():
    authorization = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6Imp5VmhRczRaUUswZ2NLR2ZlYUtnbHZ5Y05PT1JNIiwibmFtZSI6InRlc3QwMDEiLCJhcHBOdW1iZXIiOiIyNTA0MjMxODQ4MzUyNDgwMDEiLCJpbnN1ckNvbXBhbnlJZCI6IkNQOTk5OTk5OTk1NyIsImluc3VyQ29tcGFueU5hbWUiOiLkvJflronkv53pmakiLCJpbnN1ckNvbXBhbnlDb2RlIjoiMTAwNDIiLCJhcGlTZWNyZXQiOiJDRDR5K1JESmlVZ0UvaXdoSzZkTDBQUkFRK3FrbklYNmZLYndzL01NaVJVPSIsImV4cGlyZXNUaW1lIjoiMjAyNS0wNC0yNSAwODo1NjoxNSIsIm5iZiI6MTc0NTQ4NDk3NSwiZXhwIjoxNzQ1NTcxMzc1LCJpYXQiOjE3NDU0ODQ5NzUsImlzcyI6Imh0dHBzOi8vaWRlbnRpdHkubHVuei5jbiIsImF1ZCI6Imh0dHBzOi8vaWRlbnRpdHkubHVuei5jbiJ9.NNHjqKO2XYCa67WkoIfVt0NYyi0Z5T-7dKBrgB66vC4'
    header = {'token':authorization}

    data = load_workbook('src/RZ/test.xlsx')
    sheet = data.worksheets[0]
    vinList = [cell.value for cell in sheet['A'][1:] if cell.value]
    dateList = [cell.value for cell in sheet['B'][1:] if cell.value]
    
    resultList = []
    count = 0
    batch_size = 500
    title = ['车架号','日期','预测出险频率','分数','模型版本','日期范围','原始信息']
    filePath = 'src/RZ/result.xlsx'
    
    # 创建线程池，设置最大线程数为5
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        # 提交所有任务
        future_to_item = {
            executor.submit(process_single_record, vin, date, header): (vin, date) 
            for vin, date in zip(vinList, dateList)
        }
        
        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_item):
            try:
                item = future.result()
                if item:  # 只有当item不为None时才添加到结果列表
                    resultList.append(item)
                    count += 1
                    
                    # 每500条数据写入一次
                    if count % batch_size == 0:
                        utils.write_list_to_excel(resultList, title, filePath, append=(count > batch_size))
                        resultList = []  # 清空列表，准备下一批数据
                        
            except Exception as e:
                print(f"处理数据时出错: {str(e)}")

    # 保存剩余的数据
    if resultList:
        utils.write_list_to_excel(resultList, title, filePath, append=(count > batch_size))