# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py

import pymssql

from utils.utils import get_config


class SqlServerOp(object):
    def __init__(self, dbKey):
        db_config = get_config(dbKey)
        host = db_config["host"]
        user = db_config["user"]
        password = db_config["password"]
        port = db_config["port"]
        database = db_config["database"]
        self.host = host
        self.user = user
        self.password = password
        self.port = port
        self.database = database

    def getConn(self):
        connection = pymssql.connect(host=self.host, port=self.port, user=self.user, password=self.password)
        return connection


if __name__ == '__main__':
    dbinfo = SqlServerOp()
    print(dbinfo.getConn())
