# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
import json
from utils import utils
import datetime
import requests
from src.GFS.report.check_monreport_generate import check_monthlyReprot_generate
from utils.utils import get_config
from utils.mysqlOp import MysqlOp

# 获取当前日期
current_date = datetime.date.today()
# 获取当前年份
current_year = current_date.year
# 获取上一年的日期
previous_year_date = current_date.replace(year=current_year - 1)
# 获取上一年的年份
previous_year = previous_year_date.year
# 获取当前月份
current_month = current_date.replace(month=current_date.month).strftime("%m")
# 如果当前月份为1月份，上一月月份为上一年的年份+12
if current_month == "01":
    last_year_month = str(previous_year) + '-12'
    last_month = '12'
else:
    last_year_month = current_date.replace(month=current_date.month - 1).strftime("%Y-%m")
    last_month = current_date.replace(month=current_date.month - 1).strftime("%m")
# 获取机器人ID
robotId = get_config('gfs_report')['robotId']
username = '18661922397'

config_roleInfo = utils.get_config('gfs_roleInfo')
operateRoleId = config_roleInfo['operateRoleId']
groupRoleId = config_roleInfo['groupRoleId']
# 获取认证信息
Authorization = utils.get_auth_info()


# 判断通用报表是否生成，未生成进行消息预警
def push_monthlyReprot_msg(keyword):
    # 从配置文件获取需要生成的报表list
    report_list = utils.get_config('gfs_report')[keyword]
    # print(report_first)
    content = ''
    for i in range(len(report_list)):
        reportName = report_list[i]['reportName']
        reportType = report_list[i]['reportType']
        reportSql = report_list[i]['sql']
        sqlType = report_list[i]['sqlType']
        if sqlType == 1:
            sql_str = ' where Year = \'' + str(current_year) + '\'and Month =\'' + str(last_month) + '\''
        if sqlType == 2:
            sql_str = ' where ReportMonth=\'' + str(last_year_month) + '\''
        if sqlType == 3:
            sql_str = ' where CheckMonth=\'' + str(last_year_month) + '\''
        # 根据每个报表对应的查询条件拼接完整SQL
        reportSql = reportSql + sql_str
        # 调用判断报表是否成功生成的方法
        msg = check_monthlyReprot_generate('gfs_database', reportName, reportType, reportSql)
        content = content + msg
    if content:
        utils.msgPush(robotId, 'GFS线上巡检', content, username=username)


# 判断应收明细表是否生成，未生成的话进行消息预警
def push_receivableDetail_msg():
    # 从配置文件中读取对应的参数
    config_data = utils.get_config('gfs_report')['monthly_report_second_receivableDetail']
    reportType = config_data['reportType']
    reportName = config_data['reportName']
    reportSql = config_data['sql']
    sqlType = config_data['sqlType']
    apiURL = config_data['apiURL']
    # 拼接SQL
    sql = reportSql + ' where CheckMonth=\'' + str(last_year_month) + '\''
    # sql = reportSql + ' where CheckMonth=\'1920-01\''
    # 实例数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    result = utils.select(conn, sql)
    # result = utils.query_data_mysql('gfs_database', sql)
    # result1 = None
    if result:
        header = {'Content-Type': 'application/json', 'Authorization': Authorization}
        # 拼接接口信息
        jsonInfo = utils.get_json_info('/data/gfs_receivableDetail.json')
        jsonInfo['reportType'] = reportType
        jsonInfo['checkMonth'] = last_year_month
        jsonInfo['minCheckMonth'] = last_year_month
        jsonInfo['operateRoleId'] = operateRoleId
        jsonInfo['groupRoleId'] = groupRoleId
        playload = json.dumps(jsonInfo)
        reponse = requests.post(apiURL, data=playload, headers=header)
        if reponse.json()['data'] is None:
            msg = '\n' + last_month + '月份报表【' + reportName + '】后台接口未生成excel，请及时关注'
            utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)
    else:
        msg = '\n' + last_month + '月份报表【' + reportName + '】数据库表未生成数据，请及时关注'
        utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)


# 判断从ADB库获取数据的报表是否生成，未生成的话进行消息预警(业务主体应收余额表和已票表)
def push_businessReceivables_msg():
    # 从配置文件获取需要生成的报表list
    report_list = utils.get_config('gfs_report')['monthly_report_second_business']
    # print(report_first)
    content = ''
    for i in range(len(report_list)):
        reportName = report_list[i]['reportName']
        reportType = report_list[i]['reportType']
        reportSql = report_list[i]['sql']
        # 调用判断报表是否成功生成的方法
        msg = check_monthlyReprot_generate('gfs_database_ADB', reportName, reportType, reportSql)
        content = content + msg
    if content:
        utils.msgPush(robotId, 'GFS线上巡检', content, username=username)


# 判断从ADB库获取数据的报表是否生成，未生成的话进行消息预警（流通渠道逾期报表）
def push_ltOverdue_msg():
    # 从配置文件获取需要生成的报表list
    report_list = utils.get_config('gfs_report')['monthly_report_third_ltOverdue']
    # print(report_first)
    if report_list:
        reportName = report_list['reportName']
        reportSql = report_list['sql']
        apiURL = report_list['apiURL']
        # 实例数据库对象
        dbinfo = MysqlOp('gfs_database_ADB')
        conn = dbinfo.getConn()
        result1 = utils.select(conn, reportSql)
        # result1 = utils.query_data_mysql('gfs_database_ADB', reportSql)
        if result1:
            header = {'Content-Type': 'application/json', 'Authorization': Authorization}
            reponse = requests.post(apiURL, json='', headers=header)
            if reponse.json()['data'] is None:
                msg = '\n' + last_month + '月份报表【' + reportName + '】后台接口未生成excel，请及时关注'
                utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)
        else:
            msg = '\n' + last_month + '月份报表【' + reportName + '】数据库表未生成数据，请及时关注'
            utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)
        conn.close()


# 判断从ADB库获取数据的报表是否生成，未生成的话进行消息预警（逾期日报）
def push_dailyOverdue_msg():
    # 从配置文件获取需要生成的报表list
    report_list = utils.get_config('gfs_report')['daily_report_Overdue']
    # print(report_first)
    if report_list:
        reportName = report_list['reportName']
        reportSql = report_list['sql']
        apiURL = report_list['apiURL']
        # 实例数据库对象
        dbinfo = MysqlOp('gfs_database_ADB')
        conn = dbinfo.getConn()
        result1 = utils.select(conn, reportSql)
        # result1 = utils.query_data_mysql('gfs_database_ADB', reportSql)
        if result1:
            header = {'Content-Type': 'application/json', 'Authorization': Authorization}
            data = {"roleId": operateRoleId}
            reponse = requests.post(apiURL, json=data, headers=header)
            if reponse.json()['data'] is None:
                msg = '\n报表【' + reportName + '】后台接口未生成excel，请及时关注'
                utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)
        else:
            msg = '\n报表【' + reportName + '】数据库表未生成数据，请及时关注'
            utils.msgPush(robotId, 'GFS线上巡检', msg, username=username)
        # 关闭数据库连接
        conn.close()
