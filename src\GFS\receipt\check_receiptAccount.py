# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
import datetime
from decimal import Decimal

from utils.mysqlOp import MysqlOp
from utils import utils
from deepdiff import DeepDiff

# 流水确认回款金额=订单回款金额+无订单回款金额验证(需要验证到对应主体的回款金额是否一致)
# 交易流水回款金额数据核对

# 定义全局变量
today = datetime.date.today()
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday.strftime("%Y-%m-%d")


def check_receiptAccount():
    # 实例化数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    # 正式环境用
    # 1.查询出前一天进行过确认回款及取消确认回款的流水
    sql = '''select distinct  mr2.Id ,mr2.ReceiptNo ,mr2.ReceiptPrice ,mr2.RelateOrderPrice ,mr2.AuditedOrdersLessPrice  from finance_main.main_receiptrelatelog mr ,finance_main.main_receiptinfo mr2
        where mr.ReceiptId =mr2.Id
        and if (mr.RelateType=1,mr.RelateTime,mr.AuditTime) >='{0}'
        and if (mr.RelateType=1,mr.RelateTime,mr.AuditTime)<'{1}'
        and mr.Deleted =0
        and mr.AuditStatus =2
        and mr2.RelateStatus in (1,2)
        and mr2.Deleted =0
      '''.format(yesterday, today)
    # 测试环境用
    # sql = '''select distinct mr2.Id ,mr2.ReceiptNo ,mr2.ReceiptPrice ,mr2.RelateOrderPrice ,mr2.AuditedOrdersLessPrice  from finance_main.main_receiptrelatelog mr ,finance_main.main_receiptinfo mr2
    # where mr.ReceiptId =mr2.Id
    # and if (mr.RelateType=1,mr.RelateTime,mr.AuditTime) >='2023-09-01'
    # and if (mr.RelateType=1,mr.RelateTime,mr.AuditTime)<'2023-10-30'
    # and mr.Deleted =0
    # and mr.AuditStatus =2
    # and mr2.RelateStatus in (1,2)
    # and mr2.Deleted =0
    # and mr.ReceiptId='PV9999968646'
    # '''
    result = utils.select(conn, sql)
    # print('===' + str(result))
    receiptAccount_lst = []
    receiptAccount_dic = {}
    temp_dic = {}
    sql_auditedOrdersLessPrice = ""
    robotId = utils.get_config('gfs_report')['robotId']
    username = '***********'
    topic = '昨日新增回款的交易流水异常数据如下，请核实:'
    content = '\n'
    if result:
        for row in result:
            receiptAccount_dic['ReceiptId'] = row[0]
            receiptAccount_dic['ReceiptNo'] = row[1]
            receiptAccount_dic['ReceiptPrice'] = row[2]
            receiptAccount_dic['RelateOrderPrice'] = row[3]
            receiptAccount_dic['AuditedOrdersLessPrice'] = row[4]
            receiptAccount_lst.append(receiptAccount_dic)
    # print(receiptAccount_lst)
    for i in range(len(receiptAccount_lst)):
        receiptOrder_lst = []
        receiptId = receiptAccount_lst[i].get('ReceiptId')
        receiptNo = receiptAccount_lst[i].get('ReceiptNo')
        receiptPrice = receiptAccount_lst[i].get('ReceiptPrice')
        relateOrderPrice = receiptAccount_lst[i].get('RelateOrderPrice')
        auditedOrdersLessPrice = receiptAccount_lst[i].get('AuditedOrdersLessPrice')
        # 2.验证receiptinfo表流水的确认回款金额与明细表金额汇总相等
        sql_receiptOrderPrice = '''select sum(OrderReceiptPrice)  from finance_main.main_receiptorderinfo mr where ReceiptId ='{}' and Deleted =0
        '''.format(receiptId)
        result_receiptOrderPrice = utils.select(conn, sql_receiptOrderPrice)
        if result_receiptOrderPrice:
            if result_receiptOrderPrice[0][0] != relateOrderPrice:
                msg = "交易流水{}主表receiptinfo中记录的关联订单回款金额与明细表中各个订单回款金额汇总不相等".format(receiptNo)
                content = content + msg + '\n'
        # 无订单回款金额 与receiptinfo表中的AuditedOrdersLessPrice进行比较
        if auditedOrdersLessPrice != 0:
            sql_auditedOrdersLessPrice = '''select sum(if (mr2.RelateType=2,-1,1)*mr2.RelatePrice)  from finance_main.main_receiptrelate mr ,finance_main.main_receiptrelatelog mr2 
            where mr.id = mr2.RelateId 
            and mr.ReceiptId ='{}' 
            and mr.Deleted =0 
            and mr.RelateSource =6 
            and mr2.AuditStatus =2
            '''.format(receiptId)
        result_auditedOrdersLessPrice = utils.select(conn, sql_auditedOrdersLessPrice)
        if result_auditedOrdersLessPrice:
            if result_auditedOrdersLessPrice[0][0] != auditedOrdersLessPrice:
                msg = "交易流水{}主表receiptinfo中记录的无订单回款金额与回款日志表中记录的回款金额汇总不相等".format(receiptNo)
                content = content + msg + '\n'
        # 从订单的维度比对订单三层表的物料回款的金额与回款相关表记录的回款记录是否一致
        # 查询出需要比对的订单
        sql_receiptOrder = '''select bo.OrderId ,bo.OrderNo ,bo.ReceiptState from finance_main.main_receiptorderinfo mr ,finance_basic.basic_orderinfo bo 
        where mr.OrderId =bo.OrderId 
        and mr.ReceiptId ='{}'
        and bo.ReceiptState in (1,2)
        '''.format(receiptId)
        result_receiptOrder = utils.select(conn, sql_receiptOrder)
        if result_receiptOrder:
            for row in result_receiptOrder:
                temp_dic["OrderId"] = row[0]
                temp_dic["OrderNo"] = row[1]
                temp_dic["ReceiptState"] = row[2]
                receiptOrder_lst.append(temp_dic)
                temp_dic = {}
        for i in range(len(receiptOrder_lst)):
            receiptMaterial_dic = {}
            orderReceiptMaterial_dic = {}
            orderId = receiptOrder_lst[i].get("OrderId")
            orderNo = receiptOrder_lst[i].get("OrderNo")
            ReceiptState = receiptOrder_lst[i].get("ReceiptState")
            # 订单为已回款/部分回款状态,回款相关表中物料回款金额应该与订单三层表中的物料回款金额一致
            # 回款相关表中物料回款金额 (退货单的物料回款金额为负数)
            sql_receiptMaterialPrice = '''  SELECT D.OrderId,F.MainpartId,F.MainPartName,F.OrderItemDetailId,SUM(IF(C.RelateType=2,-1,1)*F.MaterialReceiptPrice) AS MaterialReceiptPrice
                        FROM finance_main.main_receiptinfo A
                        INNER JOIN finance_main.main_receiptrelate B
                        ON A.Id=B.ReceiptId AND B.RelateSource<>6 AND B.AuditStatus=3 AND B.Deleted=0
                        INNER JOIN finance_main.main_receiptrelatelog C
                        ON B.Id=C.RelateId AND C.AuditStatus=2 AND C.Deleted=0
                        INNER JOIN finance_main.main_receiptrelateorderinfolog D
                        ON C.Id=D.ReceiptRelateLogId AND D.Deleted=0
                        INNER JOIN JSON_TABLE(D.ReceiptDetailJson,'$[*]' COLUMNS(OrderItemId CHAR(12)  CHARACTER SET utf8 PATH '$.OrderItemId', 
                                                                      NESTED PATH '$.ItemDetailJson[*]' COLUMNS(OrderItemDetailId CHAR(12) CHARACTER SET utf8 PATH '$.OrderItemDetailId',
                                                                                        MainPartId CHAR(12) CHARACTER SET utf8 PATH '$.MainPartId', MainPartName VARCHAR(100) CHARACTER SET utf8 PATH '$.MainPartName',
                                                                                        MaterialReceiptPrice DECIMAL(18,4) PATH '$.MaterialReceiptPrice'))) F
                        WHERE A.Deleted=0 
                        AND D.OrderId='{}'
                        AND B.CustSettleId<> 'CR0000001429'
                        GROUP BY F.OrderItemDetailId
                        order by F.OrderItemDetailId
                        '''.format(orderId)
            result_receiptMaterialPrice = utils.select(conn, sql_receiptMaterialPrice)
            if result_receiptMaterialPrice:
                for row in result_receiptMaterialPrice:
                    if row[4] != Decimal('0.0000'):
                        temp_dic["MainpartId"] = row[1]
                        temp_dic["MaterialReceiptPrice"] = row[4]
                        receiptMaterial_dic[row[3]] = temp_dic
                        # receiptMaterial_lst.append(receiptMaterial_dic)
                        temp_dic = {}
                # print(receiptMaterial_dic)
            # 订单三层表中物料对应的回款金额
            sql_orderReceiptMaterialPrice = '''select a.OrderItemId ,a.orderitemDetailID ,a.MainPartId,a.MainPartName,(if(c.OrderType in (3,4,9,10,12),-1,1)*a.materialReceiptPrice) as materialReceiptPrice 
            from finance_basic.basic_orderitemdetail a , `finance_basic`.basic_orderitem b, `finance_basic`.basic_orderinfo c
            where a.`OrderItemId`  =b.`OrderItemId`  
            and b.`OrderId` =c.`OrderId` 
            and c.`OrderId` ='{}'
            '''.format(orderId)
            result_orderReceiptMaterialPrice = utils.select(conn, sql_orderReceiptMaterialPrice)
            if result_orderReceiptMaterialPrice:
                for row in result_orderReceiptMaterialPrice:
                    if row[4] != Decimal('0.0000'):
                        # print("继续写入")
                        temp_dic["MainpartId"] = row[2]
                        temp_dic["MaterialReceiptPrice"] = row[4]
                        orderReceiptMaterial_dic[row[1]] = temp_dic
                        temp_dic = {}
                # print(orderReceiptMaterial_dic)
            # 比对两个字典中存放的物料回款金额是否一致
            if receiptMaterial_dic != orderReceiptMaterial_dic:
                for key in receiptMaterial_dic.keys():
                    if receiptMaterial_dic[key] != orderReceiptMaterial_dic[key]:
                        diff = DeepDiff(receiptMaterial_dic[key], orderReceiptMaterial_dic[key])
                        lst = list(diff.values())
                        msg = "订单{}基础表orderItemDetail中的回款物料与回款相关表中记录的回款物料不一致,物料{}回款差异详细如下：".format(orderNo, key) + str(
                            lst).replace('"', '')
                        content = content + msg + '\n'

    if content != '\n':
        utils.msgPush(robotId, topic, content, username)
    # 关闭数据库链接
    conn.close()


# if __name__ == '__main__':
#     check_receiptAccount()
