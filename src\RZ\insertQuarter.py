from datetime import datetime, timedelta
from utils import utils

now  = datetime.now()
year = now.year
month = now.month
quarter = (month - 1) // 3 + 1

def insertQuarter():


    CreatedAt = datetime(year, 3 * quarter - 2, 1)
    if quarter == 1:
        UpdatedAt = datetime(year,3,31,23,59,59)
    elif quarter == 2:
        UpdatedAt = datetime(year,6,30,23,59,59)
    elif quarter == 3:
        UpdatedAt = datetime(year,9,30,23,59,59)
    elif quarter == 4:
        UpdatedAt = datetime(year,12,31,23,59,59)
    name = str(year)+'年第'+str(quarter)+'季度'

    sql = "INSERT INTO `zcp_custcenter`.`basic_datadictionary` (`Id`, `Code`, `Name`, `ParentId`, `SortOrder`, `LevelNo`, `Enabled`,  `CreatedAt`, `UpdatedAt`) VALUES (fn_nextval('DT'), fn_GetDatadictionaryCode('DT0000000001'), '{}', 'DT0000000001', 1, 1, 1,'{}', '{}')".format(name,CreatedAt,UpdatedAt)
    utils.query_data_mysql('rz_database', sql)

def sendMsg():
    if quarter == 1:
        SettleStartAt = datetime(year-1,10,1)
        SettleEndAt = datetime(year-1,12,31,23,59,59)
        name = str(year-1)+'年第四季度'
    elif quarter == 2:
        SettleStartAt = datetime(year,1,1)
        SettleEndAt = datetime(year,3,31,23,59,59)
        name = str(year)+'年第一季度'
    elif quarter == 3:
        SettleStartAt = datetime(year,4,1)
        SettleEndAt = datetime(year,6,30,23,59,59)
        name = str(year)+'年第二季度'
    elif quarter == 4:
        SettleStartAt = datetime(year,7,1)
        SettleEndAt = datetime(year,9,30,23,59,59)
        name = str(year)+'年第三季度'

    param = {"SettlementCycleName":name, "SettleStartAt":str(SettleStartAt), "SettleEndAt":str(SettleEndAt)}
    result = utils.call('rz_erp', '/api/v1/data/bill/schedule/sendMsg', 'POST', param=param)

    topic = 'sendMsg接口调用\n'
    errmsg = '\n参数：'+str(param)+'\n响应：'+str(result['code'])+str(result['body'])
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
    username = '17660628066'
    utils.msgPush(topic=topic, content=errmsg, webhook=webhook, username=username)
    print(param)