# -------------- 数据库/消息队列配置信息 ----------------

# 瑞行kafka
rx_kafka:
   topic: mva_device_alarm
   bootstrap_servers: ***********:9092,***********:9092,***********:9092
   group_id: mva-dev114
   auto_offset_reset: earliest
   security_protocol: SASL_PLAINTEXT
   asl_mechanism: PLAIN
   sasl_plain_username: saas3
   sasl_plain_password: saas3@Qwe123

# 瑞行doris数据库
rx_database:
   host: *************
   user: user_mva
   password: DspmqR67uns2cIjP
   port: 9030
   database: mva_pro
   charset: utf8

# 流通数据库
lt_database:
   host: af642786a50747c48c3a9a6a7f9e445epo01.internal.cn-east-3.proxy.rds.myhuaweicloud.com
   user: user_zcp_r
   password: ^9P8iZOvbp8&FqOs
   port: 3306
   database: zcs_center
   charset: utf8

# GFS数据库
gfs_database:
   host: rm-bp17v1jzdhjg40ug3.mysql.rds.aliyuncs.com
   user: user_finance_r
   password: gRAt9Z2ymQjmVSs4
   port: 3306
   database: finance_basic
   charset: utf8

# GFS ADB数据库
gfs_database_ADB:
   host: am-bp108q1py5r785iff167320.ads.aliyuncs.com
   user: user_dts
   password: aYZRm3x0GV1O1Dbg
   port: 3306
   database: ods_finance_basic
   charset: utf8

# 时有时珍数据库连接
sysz_database:
   host: af642786a50747c48c3a9a6a7f9e445epo01.internal.cn-east-3.proxy.rds.myhuaweicloud.com
   user: user_sysz
   password: q@f3^Wdkt%654ycO
   port: 3306
   database: syszcenter
   charset: utf8

# 云田物联数据库连接
ytwl_database:
   host: **************
   user: user_ytwl
   password: ilGhX@^UcSR7tffk
   port: 3306
   database: ytwlcenter_fat
   charset: utf8

# 成本预算填报工具数据库连接
budget_database:
   host: af642786a50747c48c3a9a6a7f9e445epo01.internal.cn-east-3.proxy.rds.myhuaweicloud.com
   user: user_finance_tool
   password: DoZxGoOqgzZgJ7Nb
   port: 3306
   database: finance_tool
   charset: utf8

# 瑞再数据库链接
rz_database:
   host: rm-bp1lvu241p90p59a2.mysql.rds.aliyuncs.com
   user: user_rz
   password: B6iID!LMo3U$laBY
   port: 3306
   database: zcp_custcenter
   charset: utf8

# gateway项目数据库链接
apiplat_database:
   host: rm-bp1k2c696818e8ngm.mysql.rds.aliyuncs.com
   user: yunwei
   password: NApT6AWQ
   port: 3306
   database: api_plat
   charset: utf8

# gateway项目redis链接
gateway_redis:
   host: r-bp19acnk7hbb480527.redis.rds.aliyuncs.com
   username: sre_api_pro
   password: 7em#1QQi06Rr#wGa
   port: 6379
   defaultDatabase: 10
   socket_timeout: 60
   socket_connect_timeout: 60
   decode_responses: True

# 中医药项目数据库连接
tcmsp_database:
   host: af642786a50747c48c3a9a6a7f9e445epo01.internal.cn-east-3.proxy.rds.myhuaweicloud.com
   user: user_zyy_r
   password: WOK0qcrVn0qTRGmX
   port: 3306
   database: zyy_goodscenter
   charset: utf8


# -------------- 业务系统登录认证配置信息 ----------------

# 认证中心配置
indentity:
   host: https://identity.lunz.cn
   callback_path: /connect/authorize/callback
   login_path: /account/login
   token_path: /connect/token

# 中医培训认证中心配置
indentity_tcmsp:
   host: https://tcmsp-identity.lunz.cn
   callback_path: /connect/authorize/callback
   login_path: /account/login
   token_path: /connect/token

# 云ERP登录配置
cloudERP:
  host: http://erp-gateway.lunztech.cn
  callback_param:
      scope: openid profile zcp-erp-api dc-old-outside-api lunz-sharp-template
      response_type: id_token token
      redirect_uri : http://zcp-erp.lunztech.cn/authentication/callback
      state: ca909f28e2aa45e39548ee56193b7d2a
      nonce: e64ae908e62544dcb0bc4871b072eec9
      client_id: zcp-erp
  login_param:
      button: login
      Username: blxgys
      Password: Blxgys
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1Qj0awQqQfMmfKSzxUmVrwh7AM1gTPH1hrj22E-uw5iRRhsFmjph28gu46qxy9Hwom-PcV2wHyp6Ma1zLvmqTNW0svYce1OcV3DceBGuRrn0F0XqIaNlJhUYw2ooyXaMH0

# 阳光氢能登录配置
ygqn_web:
  host: https://zcs-web-gw.lunz.cn
  callback_param:
      scope: openid profile zcs-api dc-basics-outside-api dc-old-outside-api productcenter-api sre-service-api dc-vehicles-outside-api
      response_type: id_token token
      redirect_uri : http://shop.qk.sunhydro.cn/authentication/callback
      state: 5ce243798d4f472db5653c3c3a6e7c43
      nonce: 2d5bf17628484601808ba889e2251d37
      client_id: XrVAspfiEIZjQ16LWFTXJxUstuMM9noi
  login_param:
      button: login
      Username: yqjt
      Password: Yqjt
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1RH8ap6t-1DH1dXERqA2ciFJDXnIRLZZ99pnArYnIl6SIUDLyio4x1FVws_aWuQAqAOJxBgLwE9SNzEJ7Ytzr4vnIXZlszGWvQgzvGqv8FDW00sjTaGyeid314ZlCEs5F8

# 智车商登录配置
zcs_web:
  host: http://zcs-web-gw.lunztech.cn
  callback_param:
      scope: openid profile zcs-api dc-old-outside-api productcenter-api lunz-sharp-template dc-vehicles-outside-api trusted-authentication-api app-5fac0000-3e0a-0016-48f3-08d6b7e5f445
      response_type: id_token token
      redirect_uri : http://zcs-web.lunztech.cn/authentication/callback
      state: 3ed3876390654b3782447b62bd45079a
      nonce: c7fc1cfcf2cb4a41ab1d23d5fc243f82
      client_id: zcs-web-pre
  login_param:
      button: login
      Username: yqjtmain
      Password: Yqjtmain
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1RrONGJ3WV1UTntV_stMbO_ysGb8AQ_AU91-odG0idabvsv5J_aZEEDOwbevIVGF0h1h8xX_FzVblnc1SJJnXQfA_qHuJxg8WNDZIRHk3CctPEmGeseV0l9YywTKumV25I

# 中医培训平台登录配置
tcmsp_web:
  host: https://zcs-web-gw-tcmsp.lunz.cn
  callback_param:
      scope: openid profile zcs-api dc-old-outside-api productcenter-api lunz-sharp-template dc-vehicles-outside-api trusted-authentication-api app-e6b98b82-6e55-4b76-8d95-c095bf58800d
      response_type: id_token token
      redirect_uri : https://tcmsp-web.lunz.cn/authentication/callback
      state: 88eb81cdd94b43b7be7a69d80a16a830
      nonce: 32f115f024954cb788fdd381de4a95d1
      client_id: unieetyTCM-admin-web1
  login_param:
      button: login
      Username: rlhxsuperadmin
      Password: Rlhxsuperadmin
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1R37cvRMbBMYlFWX107O3iSXL1XKdr3sKUTT3Jy5HkZxNflHJgU4xQCfXa7hbtYAXKRNpId4MooD5lYwL6l8LUDnssSvoddmiEtr4FGbx45lod3dM7K6Vn5IbDZVEGDTVw

# 中医培训平台用户中心登录配置
tcmsp_uc:
  host: https://tcmsp-ucapi.lunz.cn
  appkey: 9bee0000-3e2e-fa16-d10e-08dcfc6a28e6

# 模友登录配置
bananaRC:
   host: http://zcs-app-gw.lunztech.cn
   login_path: /api/v3/user/login
   phoneNumber: 17660628066
   password: li1234567
   channelType: 134
   scope: zcs-api offline_access dc-old-outside-api sre-service-api trusted-authentication-api
   clientId: W8QnCUUkITfl23inOfRos6PuXHOUJriH
   clientSecret: 5FG7lON9zm8GuW6XRSyMWamVLWwOczuS
   accessType: 3

# Api聚合平台登录配置
ApiPlat:
   host: https://apiplat-m-service-pro.lunz.cn
   client_id: dnMD9GavfCupvNvQQBafu0kyKAQyOpEQ
   client_secret: cuRdhE448LG3dMkNImcRyVRcDpRc77fA
   grant_type: password
   scope: openid profile apiplat-m-service-live
   username: aimaocr
   password: Aimaocr

# 数科院PingCode登录配置
PingCode_sk:
   host: https://open.pingcode.com
   login_path: /v1/auth/token?grant_type=client_credentials&client_id=RkYfOVkIjSyb&client_secret=KkTPlAmTMeuNWzgbooJXUoqz

# 中瑞PingCode登录配置
PingCode_zr:
   host: https://open.pingcode.com
   login_path: /v1/auth/token?grant_type=client_credentials&client_id=fUQWXdopZrRy&client_secret=mhKWMukUErdeTRtxBHKeKHWt

InsuranceService:
   host: https://insurance-service.lunz.cn

# 认证信息（可根据用户名和密码从认证中心获取认证信息）
Auth_info:
   host: http://identity.lunz.cn/connect/token
   username: gfsfinanceauto
   password: Gfsfinanceauto
   Authorization: Basic R0ZTQ2xpZW50OnRpMVhPTmZXV1NkTXlDZTMxNEQybjBnb2MxT0pycUF1

# 壹好车服登录配置
EHCF:
   host: https://servicestafffee-service.lunz.cn
   client_id: ServicePlatformClient
   client_secret: ServicePlatformClient
   grant_type: password
   scope: serviceplatform
   username: **********
   password: ZrZr09031437

# 瑞再登录配置
rz_erp:
   host: https://rz-erp-gw.lunzesar.com
rz_external:
   host: https://rz-external-gw.lunzesar.com

# 笑唯美登录配置
xwm:
   host: https://staff.unieety.com
   login_path: /admin/auth/token
   username: dongtong
   password: fmJdK9qBqyo2PrMZ92UK7KTtwLLAUYQ2vDU2nRoNBlERMtSxOtAWwLfO0s1NT4kfPltaBPT/rqZW5yuCGPwizMfdkALfjzqi/JMHmIDLnvLYUowCDtF3MJCEZJRiKyKsunVBvropgV+76R+422er5K1DOoalziIhJv9EDQQyJcM=

# -------------- 业务系统全局变量 ----------------

# 模友业务数据
bananaRC_data:
   postType: {1: 5,2: 5,3: 15,4: 15,5: 15,'comment': 2} #动态类型与积分的对应关系
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ebeae74a-c553-4994-9175-b445c7b381e7

# 摩友业务数据
myHome_data:
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ebeae74a-c553-4994-9175-b445c7b381e7

# 智车管家业务数据
ZCGJ_data:
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ebeae74a-c553-4994-9175-b445c7b381e7

# 瑞行企业、预警类型信息
rx_custInfo:
   custId: OI9999999676
   alarmType: 001001002,9,001001009,ext_id_0x17,001001008,ext_id_0x15,001001001,8,6,001041
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=465a513f-97c8-4665-8a80-86f0f71ba518

# YGQN配置信息
ygqn_info:
   ApplicationId: 29bda9a0-03f9-48f2-bfab-eb257a028bdc
ygqn_coupon:
   coupon10: CE9999983482
   coupon20: CE9999983481
   coupon30: CE9999983480
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec5e9f7c-725d-42e0-8290-823bda006bab
   fileKey: ec5e9f7c-725d-42e0-8290-823bda006bab

# 中医药配置信息
tcmsp_info:
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986
   fileKey: d1c59137-3c47-4b35-a686-035564d69986