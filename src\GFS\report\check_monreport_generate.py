# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
# 验证每月1号0点定频生成的报表是否成功生成
from utils import utils
import datetime
import requests
from utils.mysqlOp import MysqlOp

# 通用方法
def check_monthlyReprot_generate(keyword, rname, rtype, rsql):
    msg = ''

    # 获取当前日期
    current_date = datetime.date.today()
    # 获取当前年份
    current_year = current_date.year
    # 获取上一年的日期
    previous_year_date = current_date.replace(year=current_year - 1)
    # 获取上一年的年份
    previous_year = previous_year_date.year
    # 获取当前月份
    current_month = current_date.replace(month=current_date.month).strftime("%m")
    # 如果当前月份为1月份，上一月月份为上一年的年份+12
    if current_month == "01":
        last_year_month = str(previous_year) + '-12'
        # last_month = '12'
    else:
        last_year_month = current_date.replace(month=current_date.month - 1).strftime("%Y-%m")
        # last_month = current_date.replace(month=current_date.month - 1).strftime("%m")
    # 实例数据库对象
    dbinfo = MysqlOp(keyword)
    conn = dbinfo.getConn()
    result1 = utils.select(conn, rsql)
    # 获取认证信息
    Authorization = utils.get_auth_info()
    # 获取报表生成接口的URL
    url = utils.get_config('gfs_report')['report_generate_url']
    username = ''
    # 数据库报表数据不为空的情况下，判断后台程序是否生成excel报表
    if result1:
        header = {'Content-Type': 'application/json', 'Authorization': Authorization}
        form_data = {"checkMonth": last_year_month, "reportType": rtype}
        reponse = requests.post(url, json=form_data, headers=header)
        if reponse.json()['data'] is None:
            msg = '\n' + last_year_month + '月份报表【' + rname + '】后台接口未生成excel，请及时关注'
    else:
        msg = '\n' + last_year_month + '月份报表【' + rname + '】数据库表未生成数据，请及时关注'
    conn.close()
    return msg
