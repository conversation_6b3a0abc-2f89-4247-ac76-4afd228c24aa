from utils import utils

webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2242cb88-1c36-4144-a457-2dcafd80bcd1'

#验证各种情况下的氢罐总数量是不是和创建的氢罐总数量一致
def check_hydrogentank_count():
    #获取氢罐总数量
    sql = 'select  count(id)  from zcs_center.tb_hydrogentank  where  Deleted =0 union all '
    #获取不同生命周期下的氢罐总数量(氢罐生命周期：1.未启用；2.启用中；3.已报废；4.已丢失；5.已损坏)
    sql = sql +' select count(id)  from zcs_center.tb_hydrogentank  where  Deleted =0 and Lifecycle  in (1,2,3,4,5)  union  all '
    #获取不同氢罐状态下的氢罐总数量(氢罐状态：1.空罐；2.满罐；3.充填中；4.使用中)
    sql = sql +' select count(id)   from zcs_center.tb_hydrogentank  where  Deleted =0 and Status  in (1,2,3,4) union  all '
    #获取不同位置类型下的氢罐总数量(位置类型：1.车主；2.换罐站；3.充填站；4.氢罐调度员；5.集团；6.租车点)
    sql = sql +' select  count(id)   from  zcs_center.tb_hydrogentank  where  Deleted =0 and PlaceType  in (1,2,3,4,5,6) '
    result = utils.query_data_mysql('ygqn_database',sql)
    if result[0][0] != result[1][0] or result[0][0] != result[2][0]  or result[0][0] != result[3][0]  :
        topic = '以下情况下的氢罐总数量与创建的氢罐总数量不一致，请核对：'
        content = '\n氢罐总数量：'+ str(result[0][0])
        if result[0][0] != result[1][0] :
          content = content + '\n' + '不同生命周期下的氢罐总数量：'+ str( result[1][0]) 
        if result[0][0] != result[2][0]:
          content = content + '\n' + '不同氢罐状态下的氢罐总数量：'+ str(result[2][0] )  
        if result[0][0] != result[3][0]:
          content = content + '\n' + '不同位置类型下的氢罐总数量：'+ str(result[3][0] ) 
        utils.msgPush(webhook, topic, content, "")
       
        

    
