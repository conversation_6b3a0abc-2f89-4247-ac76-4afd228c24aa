from utils import utils

# 吉致保单推送
def jizhi_policy_push():
    param = {"filter":{}}
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
    result = utils.call('InsuranceService', '/api/v1/custPolicy/pushPolicy', 'POST', param=param)
    code = result['body']
    if code != 200:
        topic = '推送任务预警\n'
        content = '\n/api/v1/custPolicy/pushPolicy接口调用失败！\n'
        username = '17660628066'
        utils.msgPush(webhook, topic, content, username)
    else:
        print(result.content.decode('utf-8'))