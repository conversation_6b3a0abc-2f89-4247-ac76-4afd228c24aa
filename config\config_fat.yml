# -------------- 数据库/消息队列配置信息 ----------------

# 瑞行kafka
rx_kafka:
   topic: mva_device_alarm
   bootstrap_servers: *************:8066,*************:8066
   group_id: mva-dev114
   auto_offset_reset: earliest
   security_protocol: SASL_PLAINTEXT
   asl_mechanism: PLAIN
   sasl_plain_username: admin
   sasl_plain_password: admin-secret

# 瑞行doris数据库
rx_database:
   host: *************
   user: root
   password: Lunz2017
   port: 9030
   database: ods_kvehiclecenter
   charset: utf8

# 流通数据库
lt_database:
   host: **************
   user: user_zcp
   password: sSOp1JQ*ei3L2rw4
   port: 3306
   database: zcp_mallcenter
   charset: utf8

# GFS数据库
gfs_database:
   host: rm-bp17u530nslrwnk75so.mysql.rds.aliyuncs.com
   user: user_finance
   password: Lunz2017
   port: 3306
   database: finance_basic
   charset: utf8

# GFS仿真数据库
gfs_database_pre_ADB:
   host: rm-bp13n9rh3ra8c4690.mysql.rds.aliyuncs.com
   user: user_finance
   password: Lunz2017
   port: 3306
   database: finance_basic
   charset: utf8

# 时有时珍数据库连接
sysz_database:
   host: **************
   user: user_sysz
   password: ybmZDv5Oy12fzOoQ
   port: 3306
   database: syszcenter
   charset: utf8

# 云田物联数据库连接
ytwl_database:
   host: **************
   user: user_ytwl
   password: ilGhX@^UcSR7tffk
   port: 3306
   database: ytwlcenter_fat
   charset: utf8

# 成本预算填报工具数据库连接
budget_database:
   host: **************
   user: user_finance_tool
   password: cnLpfle3OOW7tFQG
   port: 3306
   database: finance_tool
   charset: utf8

# 瑞再数据库链接
rz_database:
   host: rm-bp11o6z22u80cec52eo.mysql.rds.aliyuncs.com
   user: user_rz
   password: jqnDGrzkJunl84bo
   port: 3306
   database: zcp_custcenter
   charset: utf8

# gateway项目数据库链接
apiplat_database:
   host: rm-bp11o6z22u80cec52eo.mysql.rds.aliyuncs.com
   user: user_sre_apiplat
   password: X1Sf8Qnp
   port: 3306
   database: api_plat
   charset: utf8

# gateway项目redis链接
gateway_redis:
   host: r-bp15hpbeykuxamn17bpd.redis.rds.aliyuncs.com
   username: sre_api_fat
   password: oVGlMbuv^S5QZY7Y
   port: 6379
   defaultDatabase: 45
   socket_timeout: 60
   socket_connect_timeout: 60
   decode_responses: True

# 中医药项目数据库连接
tcmsp_database:
   host: **************
   user: user_zyy
   password: HH7javtIUujfuN0b
   port: 3306
   database: zyy_goodscenter
   charset: utf8


# -------------- 业务系统登录认证配置信息 ----------------

# 认证中心配置
indentity:
   host: https://identity-fat.lunz.cn
   callback_path: /connect/authorize/callback
   login_path: /account/login
   token_path: /connect/token

# 云ERP登录配置
cloudERP:
  host: http://erp-gateway.lunztech.cn
  callback_param:
      scope: openid profile zcp-erp-api dc-old-outside-api lunz-sharp-template
      response_type: id_token token
      redirect_uri : http://zcp-erp.lunztech.cn/authentication/callback
      state: ca909f28e2aa45e39548ee56193b7d2a
      nonce: e64ae908e62544dcb0bc4871b072eec9
      client_id: zcp-erp
  login_param:
      button: login
      Username: blxgys
      Password: Blxgys
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1Qj0awQqQfMmfKSzxUmVrwh7AM1gTPH1hrj22E-uw5iRRhsFmjph28gu46qxy9Hwom-PcV2wHyp6Ma1zLvmqTNW0svYce1OcV3DceBGuRrn0F0XqIaNlJhUYw2ooyXaMH0

# 阳光氢能登录配置
ygqn_web:
  host: http://zcs-web-gw2.lunztech.cn
  callback_param:
      scope: openid profile zcs-api dc-old-outside-api productcenter-api lunz-sharp-template dc-vehicles-outside-api trusted-authentication-api
      response_type: id_token token
      redirect_uri : http://shoptest2.qk.sunhydro.cn/authentication/callback
      state: 44072b59b60f418dbda9517c3e5c9c9b
      nonce: 99ec151a657e49c19df8225fc391437c
      client_id: yq-web-test
  login_param:
      button: login
      Username: yqjtmain
      Password: Yqjtmain
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1QMpDtNboitmFgQH2fnv03QZ-_vHUazJslaixDo7aZ4cBFXZLWXW7M8Rdw7MTwJ0ZoxRJGZDWoQONmZwqf0BR98elLPBFa9ceNpeOksThR1ZIz-GpaVumoTn2iAthmBIPM

# 智车商登录配置
zcs_web:
  host: http://zcs-web-gw.lunztech.cn
  callback_param:
      scope: openid profile zcs-api dc-old-outside-api productcenter-api lunz-sharp-template dc-vehicles-outside-api trusted-authentication-api app-5fac0000-3e0a-0016-48f3-08d6b7e5f445
      response_type: id_token token
      redirect_uri : http://zcs-web.lunztech.cn/authentication/callback
      state: 3ed3876390654b3782447b62bd45079a
      nonce: c7fc1cfcf2cb4a41ab1d23d5fc243f82
      client_id: zcs-web-pre
  login_param:
      button: login
      Username: yqjtmain
      Password: Yqjtmain
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1RrONGJ3WV1UTntV_stMbO_ysGb8AQ_AU91-odG0idabvsv5J_aZEEDOwbevIVGF0h1h8xX_FzVblnc1SJJnXQfA_qHuJxg8WNDZIRHk3CctPEmGeseV0l9YywTKumV25I

# 中医培训平台登录配置
tcmsp_web:
  host: http://zcs-web-gw-tcmsp.lunztech.cn
  callback_param:
      scope: openid profile zcs-api myhome dc-old-outside-api productcenter-api lunz-sharp-template dc-vehicles-outside-api uc-users-outside-api app-e6b98b82-6e55-4b76-8d95-c095bf58800d
      response_type: id_token token
      redirect_uri : http://tcmsp-web.lunztech.cn/authentication/callback
      state: 1f68b0a4e3064c9b9045c9ca93cbd768
      nonce: 90ff72d581ff4cedbbac5f4b8009f582
      client_id: unieetyTCM-admin-web
  login_param:
      button: login
      Username: rlhxsuperadmin
      Password: Rlhxsuperadmin
      ReturnUrl: ${callback}
      __RequestVerificationToken: CfDJ8NUvc5HFTn9EnG_IMr2hg1R37cvRMbBMYlFWX107O3iSXL1XKdr3sKUTT3Jy5HkZxNflHJgU4xQCfXa7hbtYAXKRNpId4MooD5lYwL6l8LUDnssSvoddmiEtr4FGbx45lod3dM7K6Vn5IbDZVEGDTVw

# 中医培训平台用户中心登录配置
tcmsp_uc:
  host: usercenter2015.lunztech.cn

# 模友登录配置
bananaRC:
   host: http://zcs-app-gw.lunztech.cn
   login_path: /api/v3/user/login
   phoneNumber: 17660628066
   password: li1234567
   channelType: 134
   scope: zcs-api offline_access dc-old-outside-api sre-service-api trusted-authentication-api
   clientId: W8QnCUUkITfl23inOfRos6PuXHOUJriH
   clientSecret: 5FG7lON9zm8GuW6XRSyMWamVLWwOczuS
   accessType: 3

# Api聚合平台登录配置
ApiPlat:
   host: https://apiplat-m-service-pro.lunz.cn
   client_id: dnMD9GavfCupvNvQQBafu0kyKAQyOpEQ
   client_secret: cuRdhE448LG3dMkNImcRyVRcDpRc77fA
   grant_type: password
   scope: openid profile apiplat-m-service-live
   username: aimaocr
   password: Aimaocr

# 数科院PingCode登录配置
PingCode_sk:
   host: https://open.pingcode.com
   login_path: /v1/auth/token?grant_type=client_credentials&client_id=RkYfOVkIjSyb&client_secret=KkTPlAmTMeuNWzgbooJXUoqz

# 中瑞PingCode登录配置
PingCode_zr:
   host: https://open.pingcode.com
   login_path: /v1/auth/token?grant_type=client_credentials&client_id=RkYfOVkIjSyb&client_secret=KkTPlAmTMeuNWzgbooJXUoqz

InsuranceService:
   host: https://insurance-service.lunz.cn

# 认证信息（可根据用户名和密码从认证中心获取认证信息）
Auth_info:
   host: http://identity.lunz.cn/connect/token
   username: gfsfinanceauto
   password: Gfsfinanceauto
   Authorization: Basic R0ZTQ2xpZW50OnRpMVhPTmZXV1NkTXlDZTMxNEQybjBnb2MxT0pycUF1

# 壹好车服登录配置
EHCF:
   host: https://servicestafffee-service.lunz.cn
   client_id: ServicePlatformClient
   client_secret: ServicePlatformClient
   grant_type: password
   scope: serviceplatform
   username: ZR09031437
   password: ZrZr09031437

# 瑞再登录配置
rz_erp:
   host: http://rz-erp-gw.lunztech.cn
rz_external:
   host: https://rz-external-gw.lunzesar.com

# 笑唯美登录配置
xwm:
   host: https://staff.unieety.com
   login_path: /admin/auth/token
   username: a17660628066
   password: U/X/cvs6dCrayaaFFw+PFQsInn2m2gT86vOApa6sJXFpL5lTVJADa16wIseNc8sLNpIUUAvTjLj/65cANVdbOejzN2COCM+aL972Vhz32AdVgjqCgII78SIso+llBi6N1EEfjW8iHJkIcpXVjf7p9JazPQeAltHR5gAIvIBnYKs=


# -------------- 业务系统全局变量 ----------------

# 模友业务数据
bananaRC_data:
   postType: {1: 5,2: 5,3: 15,4: 15,5: 15,'comment': 2} #动态类型与积分的对应关系
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986

# 摩友业务数据
myHome_data:
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986

# 智车管家业务数据
ZCGJ_data:
   webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986

# 瑞行企业、预警类型信息
rx_custInfo:
   custId: OI9999999403
   alarmType: 001001002,9,001001009,ext_id_0x17,001001008,ext_id_0x15,001001001,8,6,001041
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986

# YGQN配置信息
ygqn_info:
   ApplicationId: 29bda9a0-03f9-48f2-bfab-eb257a028bdc
ygqn_coupon:
   coupon10: CE9999993804
   coupon20: CE9999993803
   coupon30: CE9999993802
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986
   fileKey: d1c59137-3c47-4b35-a686-035564d69986

# 中医药配置信息
tcmsp_info:
   robotId: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986
   fileKey: d1c59137-3c47-4b35-a686-035564d69986