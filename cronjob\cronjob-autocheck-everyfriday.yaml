apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-autocheck-everyfriday ## 修改名称，与yaml文件名相同即可
spec:
  schedule: "00 09 * * 5" ## 修改执行时间，格式为分钟，小时，日期，月份，星期
  concurrencyPolicy: Allow
  jobTemplate:
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: lunz-zcp-images
          containers:
          - name: autotest
            image: swr.cn-east-3.myhuaweicloud.com/lunz-zcp/qa-autocheck:snapshot
            command:
              - python
              - /app/main_everyFriday.py ## 修改为需要执行的脚本文件
            resources:
              limits:
                cpu: 1000m
                memory: 2048Mi
              requests:
                cpu: 500m
                memory: 1024Mi
            imagePullPolicy: Always
          restartPolicy: OnFailure