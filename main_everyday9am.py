from src.PingCode import notify_bugs
from src.GJF import check_data_gjf
from src.BananaRC import check_operate_info as bananarc
from src.BananaRC import check_unusual_integral
from src.ZCS import notify_sticker_data
from src.MyHome import check_operate_info as myhome
from src.ZCGJ import check_operate_info as zcgj
from src.YGQN import send_coupon

if __name__ == '__main__':
    # pingcode缺陷提醒
    notify_bugs.test_bugDailyReport_sk()
    notify_bugs.test_bugDailyReport_zr()
    # 模友运营数据提醒
    bananarc.operate_info()
    # 模友异常积分提醒
    check_unusual_integral.integral_plenty()
    # 摩友运营数据提醒
    myhome.operate_info()
    # 智车管家运营数据提醒
    zcgj.operate_info()
    # 阳氢续租优惠券发放
    send_coupon.send_coupon()


    # 莞家福运营数据提醒
    # check_data_gjf.Gjf_Data()
    # 挪车码运营数据提醒
    # notify_sticker_data.test_stickerDataNotify()