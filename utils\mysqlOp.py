# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
from utils.utils import get_config
import pymysql


class MysqlOp(object):
    def __init__(self, dbKey):
        db_config = get_config(dbKey)
        host = db_config["host"]
        user = db_config["user"]
        password = db_config["password"]
        port = db_config["port"]
        database = db_config["database"]
        charset = db_config["charset"]
        self.host = host
        self.user = user
        self.password = password
        self.port = port
        self.database = database
        self.charset = charset

    def getConn(self):
        connection = pymysql.connect(host=self.host, port=self.port, user=self.user, password=self.password,
                                     database=self.database,
                                     charset=self.charset)
        return connection
