
import datetime
from utils import utils

today = datetime.date.today().strftime("%Y-%m-%d")
yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")

webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9041f70f-1a7c-480f-86d2-311590f454c1'

def Gjf_Data():
    sql_Customer_Total="SELECT count(0)  FROM zcs_center.vi_custinfo WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334') AND (RelationType = 0 AND CustState = 1 AND Deleted = false )"
    sql_Customer_Yesterday="SELECT count(0)  FROM zcs_center.vi_custinfo WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334') AND (RelationType = 0 AND CustState = 1 AND Deleted = false and DATE(CreatedAt)= '%s')" % yesterday
    sql_Survry_Record_Yesterday="select count(b.Id)  from  zcp_custcenter.co_surveyinfo a left join zcp_custcenter.co_survey_record b on a.Id = b.SurveyId and  a.DepartId ='CP9999961334' and b.Deleted = 0 and DATE(b.CreatedAt)= '%s' " % yesterday
    sql_Survry_Record_Total="select count(b.Id)  from  zcp_custcenter.co_surveyinfo a left join zcp_custcenter.co_survey_record b on a.Id = b.SurveyId and  a.DepartId ='CP9999961334' and b.Deleted = 0" 
    sql_Coupon_Record_Total="SELECT COUNT(0) FROM zcs_membercenter.vi_coupon_record WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334')" 
    sql_Coupon_Record_Yesterday="SELECT COUNT(0) FROM zcs_membercenter.vi_coupon_record WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334')and DATE(CreatedAt)= '%s' " % yesterday
    sql_Coupon_Writeoff_Yesterday="SELECT COUNT(0) FROM zcs_membercenter.vi_coupon_writeoff WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334') AND (BusinessType = 3) and DATE(CreatedAt)= '%s' " % yesterday
    sql_Coupon_Writeoff_Total="SELECT COUNT(0) FROM zcs_membercenter.vi_coupon_writeoff WHERE (DepartId='CP9999961334' OR ParentDepartId='CP9999961334') AND (BusinessType = 3)"
    result_Customer_Total = utils.query_data_mysql('lt_database',sql_Customer_Total)
    result_Customer_Yesterday = utils.query_data_mysql('lt_database',sql_Customer_Yesterday)
    result_Survry_Record_Yesterday = utils.query_data_mysql('lt_database',sql_Survry_Record_Yesterday)
    result_Survry_Record_Total = utils.query_data_mysql('lt_database',sql_Survry_Record_Total)
    result_Coupon_Record_Total = utils.query_data_mysql('lt_database',sql_Coupon_Record_Total)
    result_Coupon_Record_Yesterday = utils.query_data_mysql('lt_database',sql_Coupon_Record_Yesterday)
    result_Coupon_Writeoff_Total = utils.query_data_mysql('lt_database',sql_Coupon_Writeoff_Total)
    result_Coupon_Writeoff_Yesterday = utils.query_data_mysql('lt_database',sql_Coupon_Writeoff_Yesterday)
    username = ''
    topic ='莞家福数据统计'+'\n'
    content = '\n'+'累计注册用户数量 ：'+ str(result_Customer_Total[0][0])+'\n'+'昨日注册用户数量：'+ str(result_Customer_Yesterday[0][0])+'\n'+'累计答题次数：'+ str( result_Survry_Record_Total[0][0])+'\n'+'昨日答题次数：'+ str( result_Survry_Record_Yesterday[0][0])+'\n'+'累计领券数量：'+ str( result_Coupon_Record_Total[0][0])+'\n'+'昨日领券数量：'+ str( result_Coupon_Record_Yesterday[0][0])+'\n'+'累计核销次数：'+ str( result_Coupon_Writeoff_Total[0][0])+'\n'+'昨日核销次数：'+ str( result_Coupon_Writeoff_Yesterday[0][0])
    utils.msgPush(webhook, topic,content,username)
       