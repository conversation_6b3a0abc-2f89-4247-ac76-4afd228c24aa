# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
# -- coding: utf-8 --

import datetime
from utils import utils
from utils.mysqlOp import MysqlOp

# 定义全局变量
today = datetime.date.today()
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday.strftime("%Y-%m-%d")


# 已生效的调价单主子表数据核对
def check_adjustOrderPrice():
    # 实例数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    # 查询出前一天已生效的调价单
    # 正式环境用
    sql = '''select id,AdjustPriceNo ,OrderTotalPrice ,AdjustOrderTotalPrice ,GoodsTotalPrice ,AdjustGoodsTotalPrice
    from `finance_main`.`main_adjustpriceinfo`
    where deleted =0
    and AuditTime >='{0}'
    and AuditTime <'{1}'
    and AdjustPriceStatus =3
      '''.format(yesterday, today)
    # 测试环境用
    # sql = '''select id,AdjustPriceNo ,OrderTotalPrice ,AdjustOrderTotalPrice ,GoodsTotalPrice ,AdjustGoodsTotalPrice
    # from `finance_main`.`main_adjustpriceinfo`
    # where deleted =0
    # and `AdjustPriceNo` ='TJD-2023111013074187'
    # and AdjustPriceStatus =3
    # '''
    # result = utils.sql_excute('gfs_database', sql)
    result = utils.select(conn, sql)
    adjustOrder_lst = []
    adjustOrder_dic = {}
    robotId = utils.get_config('gfs_report')['robotId']
    username = '18661922397'
    topic = '昨日新增调价单异常数据如下，请核实:'
    content = '\n'
    if result:
        for row in result:
            adjustOrder_dic['Id'] = row[0]
            adjustOrder_dic['AdjustPriceNo'] = row[1]
            adjustOrder_dic['OrderTotalPrice'] = row[2]
            adjustOrder_dic['AdjustOrderTotalPrice'] = row[3]
            adjustOrder_dic['GoodsTotalPrice'] = row[4]
            adjustOrder_dic['AdjustGoodsTotalPrice'] = row[5]
            adjustOrder_lst.append(adjustOrder_dic)
            adjustOrder_dic = {}
    # print(adjustOrder_lst)
    for i in range(len(adjustOrder_lst)):
        adjustOrderId = adjustOrder_lst[i].get('Id')
        adjustPriceNo = adjustOrder_lst[i].get('AdjustPriceNo')
        orderTotalPrice = adjustOrder_lst[i].get('OrderTotalPrice')
        adjustOrderTotalPrice = adjustOrder_lst[i].get('AdjustOrderTotalPrice')
        goodsTotalPrice = adjustOrder_lst[i].get('GoodsTotalPrice')
        adjustGoodsTotalPrice = adjustOrder_lst[i].get('AdjustGoodsTotalPrice')
        sql_adjustOrder = '''select sum(OrderPrice) as OrderTotalPrice,sum(OrderAdjustPrice) as AdjustOrderTotalPrice 
        from `finance_main`.`main_adjustpriceorderinfo` 
        where `AdjustPriceId` ='{}' 
        and Deleted =0
        '''.format(adjustOrderId)
        # result_adjustOrder = utils.sql_excute('gfs_database', sql_adjustOrder)
        result_adjustOrder = utils.select(conn, sql_adjustOrder)
        if result_adjustOrder:
            if result_adjustOrder[0][0] != orderTotalPrice:
                msg = "调价单{}调价前订单总金额与各个订单金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
            if result_adjustOrder[0][1] != adjustOrderTotalPrice:
                msg = "调价单{}调价后订单总金额与各个订单金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
        # 需要考虑退货单
        sql_adjustGoods = '''select sum(GoodsPrice * GoodsAdjustAmount) as goodsTotalPrice,sum(GoodsAdjustPrice * GoodsAdjustAmount) as adjustGoodsTotalPrice 
        from `finance_main`.`main_adjustpriceitem` 
        where `AdjustPriceId` ='{}' 
        and Deleted =0
        '''.format(adjustOrderId)
        # result_adjustGoods = utils.sql_excute('gfs_database', sql_adjustGoods)
        result_adjustGoods = utils.select(conn, sql_adjustGoods)
        if result_adjustGoods:
            if result_adjustGoods[0][0] != goodsTotalPrice:
                msg = "调价单{}调价前商品总金额与各个商品金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
            if result_adjustGoods[0][1] != adjustGoodsTotalPrice:
                msg = "调价单{}调价后商品总金额与各个商品金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
        # 需要考虑退货单
        sql_adjustMaterials = '''select sum(a.MaterialPrice * GoodsAdjustAmount) as goodsTotalPrice,sum(a.MaterialAdjustPrice * GoodsAdjustAmount) as adjustGoodsTotalPrice from finance_main.main_adjustpriceitemdetail a,finance_main.main_adjustpriceitem b 
        where a.AdjustPriceItemId  = b.Id 
        and b.AdjustPriceId ='{}'
        and a.Deleted =0
        '''.format(adjustOrderId)
        # result_adjustMaterials = utils.sql_excute('gfs_database', sql_adjustMaterials)
        result_adjustMaterials = utils.select(conn, sql_adjustMaterials)
        if result_adjustMaterials:
            if result_adjustMaterials[0][0] != goodsTotalPrice:
                msg = "调价单{}调价前商品总金额与各个商品所含物料金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
            if result_adjustMaterials[0][1] != adjustGoodsTotalPrice:
                msg = "调价单{}调价后商品总金额与各个商品所含物料金额汇总不相等".format(adjustPriceNo)
                content = content + msg + '\n'
    if content != '\n':
        utils.msgPush(robotId, topic, content, username)
    # 关闭数据库链接
    conn.close()


# 从调价的订单维度验证调价差价
def check_orderAdjustPrice():
    # 实例数据库对象
    dbinfo = MysqlOp('gfs_database')
    conn = dbinfo.getConn()
    # 正式环境用
    sql = '''select ma2.OrderId ,ma2.OrderNo  from finance_main.main_adjustpriceinfo ma  ,finance_main.main_adjustpriceorderinfo ma2
        where ma.Id = ma2.AdjustPriceId
        and ma.AdjustPriceStatus =3
        and ma.AuditTime >='{0}'
        and ma.AuditTime <'{1}'
        and ma.Deleted =0
        and ma2.Deleted =0
        '''.format(yesterday, today)
    # 测试环境用
    # sql = '''select ma2.OrderId ,ma2.OrderNo  from finance_main.main_adjustpriceinfo ma  ,finance_main.main_adjustpriceorderinfo ma2
    # where ma.Id = ma2.AdjustPriceId
    # and ma.AdjustPriceStatus =3
    # and ma.AuditTime >='2023-10-01'
    # and ma.AuditTime <'2023-12-20'
    # and ma.Deleted =0
    # and ma2.Deleted =0
    # '''
    # result = utils.sql_excute('gfs_database', sql)
    result = utils.select(conn, sql)
    orderAdjust_lst = []
    orderAdjust_dic = {}
    robotId = utils.get_config('gfs_report')['robotId']
    username = '18661922397'
    topic = '昨日新增调价订单异常数据如下，请核实:'
    content = '\n'
    if result:
        for row in result:
            orderAdjust_dic['OrderId'] = row[0]
            orderAdjust_dic['OrderNo'] = row[1]
            orderAdjust_lst.append(orderAdjust_dic)
            orderAdjust_dic = {}
    # print(orderAdjust_lst)
    for i in range(len(orderAdjust_lst)):
        orderAdjustId = orderAdjust_lst[i].get('OrderId')
        orderAdjustNo = orderAdjust_lst[i].get('OrderNo')
        sql_orderAdjust = '''select (OrderAdjustPrice-OrderPrice)*if (OrderType in (3,4,9,10,12),-1,1) as adjustPrice 
        from finance_basic.basic_orderinfo bo 
        where bo.OrderId ='{}'
        '''.format(orderAdjustId)
        sql_goodsAdjust = '''select sum((ifnull(GoodsAdjustPrice,GoodsPrice) -GoodsPrice) * GoodsAmount) as goodsAdjustPrice 
        from finance_basic.basic_orderitem bo where bo.OrderId ='{}'
        '''.format(orderAdjustId)
        sql_materialAdjust = '''select sum((ifnull(MaterialAdjustPrice,MaterialPrice)-MaterialPrice) * MaterialAmount) as materialAdjustPrice 
        from finance_basic.basic_orderitemdetail bo 
        where bo.OrderItemId  in (
        select OrderItemId  from finance_basic.basic_orderitem bo where bo.OrderId ='{}'
         )
        '''.format(orderAdjustId)
        # 订单基础表调价差价和订单业务表进行比对
        sql_adjustOrder = '''select sum(ma.OrderAdjustPrice-ma.OrderPrice)* if (OrderType in (3,4,9,10,12),-1,1) as adjustOrderPrice 
               from finance_main.main_adjustpriceorderinfo ma ,finance_main.main_adjustpriceinfo ma2, finance_basic.basic_orderinfo c
               where ma.AdjustPriceId  = ma2.Id
               and ma.OrderId =c.OrderId
               and ma2.AdjustPriceStatus =3
               and ma.Deleted =0
               and ma2.Deleted =0
               and ma.OrderId ='{}'
               '''.format(orderAdjustId)
        result_orderAdjust = utils.select(conn, sql_orderAdjust)
        result_goodsAdjust = utils.select(conn, sql_goodsAdjust)
        result_materialAdjust = utils.select(conn, sql_materialAdjust)
        result_adjustOrder = utils.select(conn, sql_adjustOrder)
        if result_orderAdjust and result_goodsAdjust and result_materialAdjust and result_orderAdjust:
            if result_orderAdjust[0][0] != result_goodsAdjust[0][0]:
                msg = "订单{}订单层的调价差价与商品层的调价差价不一致".format(orderAdjustNo)
                content = content + msg + '\n'
            if result_goodsAdjust[0][0] != result_materialAdjust[0][0]:
                msg = "订单{}商品层的调价差价与物料层的调价差价不一致".format(orderAdjustNo)
                content = content + msg + '\n'
            if result_orderAdjust[0][0] != result_materialAdjust[0][0]:
                msg = "订单{}订单层的调价差价与物料层的调价差价不一致".format(orderAdjustNo)
                content = content + msg + '\n'
            if result_adjustOrder[0][0] != result_orderAdjust[0][0]:
                msg = "订单{}订单层的调价差价与调价关联订单表的调价差价不一致".format(orderAdjustNo)
                content = content + msg + '\n'
    if content != '\n':
        # print(content)
        utils.msgPush(robotId, topic, content, username)
    # 关闭数据库链接
    conn.close()

#
# if __name__ == '__main__':
#     check_orderAdjustPrice()
#     check_adjustOrderPrice()



