from utils import utils


webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ebeae74a-c553-4994-9175-b445c7b381e7'
username = ''

def post_frequently():
    sql = '''select UserName, count(Id)
        from
            zcp_commentcenter.cm_postinfo cp
        where
            CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -10 minute), '%Y-%m-%d %H:%m:00'))
            and CreatedAt < CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d %H:%m:00'))
            and ChannelType = 134
            and Deleted = 0
        group by
            UserName'''
    result = utils.query_data_mysql('lt_database',sql)
    if result:
        topic = '以下用户近10分钟内发表作品超过3篇，请核对：\n'
        content = ''
        arr = []
        for record in result:
            if record[1] > 3:
                arr.append(record)
                content = content + '\n' + str(record[0]) +'  发表作品数：'+ str(record[1])
        if arr:
            utils.msgPush(webhook, topic, content, username)

def integral_plenty():

    sql = '''select tc.LinkmanTel, sum(tir.ChangeNumber)
        from
            zcs_membercenter.tb_integral_record tir,
            zcs_center.tb_custinfo tc
        where
            tir.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
            and tir.CreatedAt < CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
            and tir.ChannelType = 134
            and tir.ChangeType = 1
            and tir.State = 2
            and tir.Deleted = 0
            and tir.CustId = tc.Id
        group by
            tc.id'''
    result = utils.query_data_mysql('lt_database',sql)
    if result:
        topic = '以下用户昨日获取积分大于1000分，请核对：\n'
        content = ''
        arr = []
        for record in result:
            if record[1] > 1000:
                arr.append(record)
                content = content + '\n' + str(record[0]) +'  '+ str(record[1]) + '分'
        if arr:
            utils.msgPush(webhook, topic, content, username)

# def integral_reduce_calculate():

#     sql_post = 'select UserName, PostType from zcp_commentcenter.cm_postinfo cp where DeletedAt >= \''+yesterday+'\' and DeletedAt < \''+today+'\' and CheckType = 2 and ChannelType = 134 and Deleted = 1 order by UserName'
#     sql_comment = 'select tu.LinkmanTel from zcp_commentcenter.cm_post_comment cpc, zcp_commentcenter.cm_postinfo cp, zcs_center.tb_userinfo tu where cpc.DeletedAt >= \''+yesterday+'\' and cpc.DeletedAt < \''+today+'\' and cpc.CheckType = 2 and cpc.Deleted = 1 and cp.ChannelType = 134 and tu.Id = cpc.UserId and cp.Id = cpc.PostId order by tu.LinkmanTel'
#     sql_commentDetail = 'select tu.LinkmanTel from zcp_commentcenter.cm_post_commentdetail cpc, zcp_commentcenter.cm_postinfo cp, zcs_center.tb_userinfo tu where cpc.DeletedAt >= \''+yesterday+'\' and cpc.DeletedAt < \''+today+'\' and cpc.CheckType = 2 and cpc.Deleted = 1 and cp.ChannelType = 134 and tu.Id = cpc.UserId and cp.Id = cpc.PostId order by tu.LinkmanTel'
#     postRange = utils.get_config('bananaRC_postType')
#     commentIntegral = postRange['comment']
#     postArr = utils.query_data_mysql('lt_database', sql_post)
#     userArr = []
#     recordArr = {}
#     if postArr:
#         # 拿到去重之后的手机号list
#         for user in postArr:
#             if user[0] not in userArr:
#                 userArr.append(user[0])
#         # 循环使用每个手机号到发帖列表里搜索，如果找到该手机号则累计对应作品类型的积分
#         for user in userArr:
#             integral = 0
#             for post in postArr:
#                 if post[0] == user:
#                     # 如果作品类型在扣减积分的范围内，累计该类型对应的积分
#                     if post[1] in postRange:
#                         integral = integral + postRange[post[1]]
#             # 如果该用户累计积分大于0，放到预警用户list里，继续循环下一个用户
#             if integral > 0:
#                 recordArr[user] = integral

#     commentArr = utils.query_data_mysql('lt_database', sql_comment)
#     if commentArr: 
#         for comment in commentArr:
#             # 如果用户不在预警用户list里，把这个用户加进去，累计一次评论对应的积分
#             if comment[0] not in recordArr.keys():
#                 recordArr[comment[0]] = commentIntegral
#             # 如果用户在预警用户list里，修改用户积分为现有积分+一次评论对应的积分，如果有重复数据则重复累加
#             else:
#                 recordArr[comment[0]] = recordArr[comment[0]] + commentIntegral

#     commentDetailArr = utils.query_data_mysql('lt_database', sql_commentDetail)
#     if commentDetailArr:
#         for commentDetail in commentDetailArr:
#             # 如果用户不在预警用户list里，把这个用户加进去，累计一次评论对应的积分
#             if commentDetail[0] not in recordArr.keys():
#                 recordArr[commentDetail[0]] = commentIntegral
#             # 如果用户在预警用户list里，修改用户积分为现有积分+一次评论对应的积分，如果有重复数据则重复累加
#             else:
#                 recordArr[commentDetail[0]] = recordArr[commentDetail[0]] + commentIntegral

#     if recordArr:
#         topic = '以下用户昨日存在被删除的作品及评论，请扣减相应积分：'
#         content = ''
#         for record in recordArr:
#             content = content + '\n' + record + '  ' + str(recordArr[record]) + '分'
#         utils.msgPush(webhook, topic, content, username)
