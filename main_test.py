# -*- coding: utf-8 -*-
from openai import OpenAI

if __name__ == '__main__':

    client = OpenAI(api_key="sk-LndlgUgMrSZXmzv1k6OCurzGBW0qIs69lemXs4RbK3Mlv5CZ", base_url="https://tbnx.plus7.plus/v1")

    # 构造对话请求
    response = client.chat.completions.create(
        model="deepseek-chat",  # R1模型标识符[[1,2,15]]
        messages=[
            {"role": "system", "content": "你是一个专业的助手"},
            {"role": "user", "content": """我大概一个星期前开始觉得胸口闷闷的，有时候还会觉得有点疼，
             特别是晚上躺下的时候更明显。最开始我以为是累的，就没太在意。但是这几天越来越频繁了，
             有时候还会觉得喘不上气来。我尝试了深呼吸，感觉也没太大用。昨天晚上我疼得厉害，
             一晚上都没睡好。除了胸口疼，我还觉得有点头晕，有时候还会觉得恶心，但是没吐出来。
             我这几天也没吃什么特别的东西，就是正常吃饭。我之前身体一直都还不错，没有什么大病。
             就是两年前体检的时候查出有点高血压，但是我一直有在吃药控制，平时也会注意少吃盐。
             其他就没什么了，我也没什么过敏史。根据以上对话分析病人的主诉、现病史和既往史"""}
        ],
        temperature=0.7,
        stream=False  # 如需流式响应可设为True
    )

    # 输出响应结果
    print(response.choices[0].message.content)