# -- coding: utf-8 --
# @time :
# <AUTHOR> Lxf
# @file : .py
from src.GFS.report.push_report_generate_msg import push_monthlyReprot_msg
from src.GFS.report.push_report_generate_msg import push_receivableDetail_msg
from src.GFS.report.push_report_generate_msg import push_businessReceivables_msg
from src.GFS.report.push_report_generate_msg import push_ltOverdue_msg
from src.GFS.report.push_report_generate_msg import push_dailyOverdue_msg
if __name__ == '__main__':
    # 每月1号零点生成的通用报表
    # 损益表-调价单/损益表-调价单（智投）/回款明细表（智投）
    push_monthlyReprot_msg('monthly_report_first')
    # 每月2号零点生成的通用报表
    # 损益表-回款报表
    push_monthlyReprot_msg('monthly_report_second')
    # 每月3号零点生成的通用报表
    # 报税专用表
    push_monthlyReprot_msg('monthly_report_third')
    # 每月2号零点生成的应收明细报表
    push_receivableDetail_msg()
    # # 每月2号零点生成的ADB库报表
    # # 业务主体应收余额表/业务主体应收余额已票表
    # push_businessReceivables_msg()
    # # 每月3号零点生成的ADB库报表
    # # 流通渠道逾期报表
    # push_ltOverdue_msg()
    # # 每日零点生成的ADB库报表
    # # 逾期报表-日报
    # push_dailyOverdue_msg()
