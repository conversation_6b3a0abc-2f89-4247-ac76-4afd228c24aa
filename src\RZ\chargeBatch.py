from utils import utils
import json

def chargeBatchOnTime():
    result = utils.call('rz_erp', '/api/v1/data/logs/updateChargeBatchOnTime', 'POST', param={})
    errmsg = ''
    print(result['code'], result['body'])
    if result['code'] != 200:
        errmsg = 'updateChargeBatchOnTime接口报错：\n'+str(result['body'])
    else:
        isSuccess = utils.json_path(result['body'], '$.data.isSuccess')
        if isSuccess[0] != '1':
            errmsg = 'updateChargeBatchOnTime接口报错：\n'+str(result['body'])
    if errmsg:
        topic = 'updateChargeBatchOnTime接口报错\n'
        username = '17660628066'
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c59137-3c47-4b35-a686-035564d69986'
        errmsg = '\n'+errmsg
        utils.msgPush(webhook=webhook, topic=topic, content=errmsg, username=username)
    