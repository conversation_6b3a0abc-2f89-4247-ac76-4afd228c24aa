from utils import utils



def operate_info():

    sql_user_num = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 9'''
    sql_user_yesterday_increase = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 9
            and b.CreatedAt >= CONCAT(DATE_FORMAT(DATE_ADD(NOW(), interval -1 day), '%Y-%m-%d'), ' 00:00:00')
            and b.CreatedAt<CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
            '''
    sql_user_month_increase = '''
        select 
            count(1)
        from
            zcs_center.tb_userinfo a
        inner join zcs_center.tb_userchannelinfo b 
        on
            a.id = b.userid
            and b.Deleted = 0
        where
            a.Deleted = 0
            and a.IsLogout = 0
            and b.ChannelType = 9
            and b.CreatedAt >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m'), '-01 00:00:00')
            and b.CreatedAt<now()'''
    
    webhook = utils.get_config('ZCGJ_data')['webhook']

    dbAlias = 'lt_database'
    user_num = utils.query_data_mysql(dbAlias, sql_user_num)
    user_yesterday_increase = utils.query_data_mysql(dbAlias, sql_user_yesterday_increase)
    user_month_increase = utils.query_data_mysql(dbAlias, sql_user_month_increase)

    topic = '<font color=\"info\">智车管家</font> 监控数据统计'
    content = f'''
    >用户总数：{user_num[0][0]}
    >昨日新增：{user_yesterday_increase[0][0]}
    >当月新增：{user_month_increase[0][0]}'''
    utils.msgPush_markdown(webhook=webhook, topic=topic, content=content)









