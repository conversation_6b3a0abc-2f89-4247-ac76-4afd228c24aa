from utils import utils

def check_api():
    """
    检查API的函数

    该函数执行以下步骤：
    1. 发送一个OPTIONS请求以获取当前用户的菜单项目。
    2. 获取身份认证信息。
    3. 发送一个GET请求以获取用户信息。
    4. 从配置中获取appkey。
    5. 从用户信息中提取authtoken。
    6. 使用appkey和authtoken发送一个GET请求以获取当前用户的菜单项目。
    7. 打印最终结果。
    """

    # 设置请求头，包含跨域请求相关信息
    headers = {
        "origin": "https://tcmsp-web.lunz.cn",
        "access-control-request-method": "GET",
        "access-control-request-headers": "appkey,authorization,authtoken,x-content-type-options,x-xss-protection",
        "sec-fetch-mode": "cors",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "host": "tcmsp-ucapi.lunz.cn"
    }

    # 发送OPTIONS请求以获取当前用户的菜单项目
    result = utils.call('tcmsp_uc', '/api//membership/getCurrentUserMenuItems', headers=headers, method='OPTIONS')

    # 获取身份认证信息
    headers = utils.get_indentity_implicit_auth('tcmsp_web', 'indentity_tcmsp')

    # 发送GET请求以获取用户信息
    result = utils.call('indentity_tcmsp', '/connect/userinfo', headers=headers, method='GET')

    # 从配置中获取appkey
    appkey = utils.get_config('tcmsp_uc')['appkey']

    # 从用户信息中提取authtoken
    authtoken = utils.json_path(result['body'], '$.authToken')

    # 设置请求头，包含appkey和authtoken
    headers = {"appkey": appkey, "authtoken": authtoken[0]}

    # 发送GET请求以获取当前用户的菜单项目
    result = utils.call('tcmsp_uc', '/api//membership/getCurrentUserMenuItems', headers=headers, method='GET')

    # 打印最终结果
    print(result)